(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6],{1867:(l,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=s(5155);s(2115);var i=s(6766),r=s(9858),d=s(3582);let c=()=>{let l={email:"Enter your email address",password:"Enter your password"};return(0,a.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)(i.default,{src:"/images/bgauthimg.png",alt:"auth",fill:!0,className:"object-cover",priority:!0})}),(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,a.jsx)(i.default,{src:"/images/bgauthimg.png",alt:"auth",fill:!0,className:"object-cover",priority:!0})}),(0,a.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,a.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,a.jsx)(r.A,{formType:"SIGN_IN",schema:d.Il,defaultValues:{email:"",password:""},onSubmit:l=>Promise.resolve({success:!0,data:l}),heading:"Sign In - For Applicants",placeholderValues:l})})})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,a.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,a.jsx)("div",{className:"w-full max-w-md",children:(0,a.jsx)(r.A,{formType:"SIGN_IN",schema:d.Il,defaultValues:{email:"",password:""},onSubmit:l=>Promise.resolve({success:!0,data:l}),heading:"Sign In - For Applicants",placeholderValues:l})})})})})]})}},9559:(l,e,s)=>{Promise.resolve().then(s.bind(s,1867))}},l=>{var e=e=>l(l.s=e);l.O(0,[766,831,903,671,874,421,441,684,358],()=>e(9559)),_N_E=l.O()}]);