import { InterviewService } from './interview.service';
import { InterviewRequest, InterviewResponse, ConversationMessage } from './interface/interview.interface';
export declare class InterviewController {
    private readonly interviewService;
    constructor(interviewService: InterviewService);
    runInterview(interviewData: InterviewRequest): Promise<InterviewResponse>;
    analyzeVideo(data: {
        transcript: ConversationMessage[];
    }): Promise<import("./interface/interview.interface").VideoScores>;
}
