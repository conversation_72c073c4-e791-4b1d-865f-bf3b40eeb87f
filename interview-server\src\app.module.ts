import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { InterviewModule } from './interview/interview.module';
import { SpeechToTextModule } from './speech-to-text/speech-to-text.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    InterviewModule,
    // SpeechToTextModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
