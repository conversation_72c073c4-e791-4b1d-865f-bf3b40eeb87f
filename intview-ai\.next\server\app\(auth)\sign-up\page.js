(()=>{var e={};e.id=220,e.ids=[220],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3660:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(auth)\\\\sign-up\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},4793:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>d});var s=t(5239),i=t(8088),n=t(8170),o=t.n(n),a=t(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["(auth)",{children:["sign-up",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3660)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,7470)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(auth)/sign-up/page",pathname:"/sign-up",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5414:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(687);t(3210);var i=t(474),n=t(2977),o=t(9360);let a=()=>{let e={email:"Enter your email address",name:"Enter your full name",password:"Enter your password",confirmPassword:"Confirm your password"};return(0,s.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,s.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,s.jsx)(i.default,{src:"/images/bgauthimg.png",alt:"auth",fill:!0,className:"object-cover",priority:!0})}),(0,s.jsxs)("div",{className:"lg:hidden",children:[(0,s.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,s.jsx)(i.default,{src:"/images/bgauthimg.png",alt:"auth",fill:!0,className:"object-cover",priority:!0})}),(0,s.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,s.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,s.jsx)(n.A,{formType:"SIGN_UP",schema:o.mJ,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:!0,data:e}),heading:"Sign Up - For Applicants",placeholderValues:e})})})]}),(0,s.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,s.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,s.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,s.jsx)("div",{className:"w-full max-w-md",children:(0,s.jsx)(n.A,{formType:"SIGN_UP",schema:o.mJ,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:!0,data:e}),heading:"Sign Up - For Applicants",placeholderValues:e})})})})})]})}},5511:e=>{"use strict";e.exports=require("crypto")},5607:(e,r,t)=>{Promise.resolve().then(t.bind(t,5414))},8655:(e,r,t)=>{Promise.resolve().then(t.bind(t,3660))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[97,423,598,23,937,814,338],()=>t(4793));module.exports=s})();