import { ConfigService } from '@nestjs/config';
import { InterviewRequest, InterviewResponse, ConversationMessage, VideoScores } from './interface/interview.interface';
export declare class InterviewService {
    private configService;
    private readonly gemini;
    constructor(configService: ConfigService);
    runInterview(interviewData: InterviewRequest): Promise<InterviewResponse>;
    analyzeVideoTranscript(transcript: ConversationMessage[]): Promise<VideoScores>;
}
