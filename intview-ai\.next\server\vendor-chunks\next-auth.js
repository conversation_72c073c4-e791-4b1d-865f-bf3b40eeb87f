"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-auth/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthError: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.AuthError),\n/* harmony export */   CredentialsSignin: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.CredentialsSignin),\n/* harmony export */   customFetch: () => (/* reexport safe */ _auth_core__WEBPACK_IMPORTED_MODULE_0__.customFetch),\n/* harmony export */   \"default\": () => (/* binding */ NextAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var _lib_env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/env.js */ \"(rsc)/./node_modules/next-auth/lib/env.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/index.js */ \"(rsc)/./node_modules/next-auth/lib/index.js\");\n/* harmony import */ var _lib_actions_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/actions.js */ \"(rsc)/./node_modules/next-auth/lib/actions.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @auth/core/errors */ \"(rsc)/./node_modules/@auth/core/errors.js\");\n/**\n * _If you are looking to migrate from v4, visit the [Upgrade Guide (v5)](https://authjs.dev/getting-started/migrating-to-v5)._\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install next-auth@beta\n * ```\n *\n * ## Environment variable inference\n *\n * `NEXTAUTH_URL` and `NEXTAUTH_SECRET` have been inferred since v4.\n *\n * Since NextAuth.js v5 can also automatically infer environment variables that are prefixed with `AUTH_`.\n *\n * For example `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` will be used as the `clientId` and `clientSecret` options for the GitHub provider.\n *\n * :::tip\n * The environment variable name inferring has the following format for OAuth providers: `AUTH_{PROVIDER}_{ID|SECRET}`.\n *\n * `PROVIDER` is the uppercase snake case version of the provider's id, followed by either `ID` or `SECRET` respectively.\n * :::\n *\n * `AUTH_SECRET` and `AUTH_URL` are also aliased for `NEXTAUTH_SECRET` and `NEXTAUTH_URL` for consistency.\n *\n * To add social login to your app, the configuration becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth({ providers: [ GitHub ] })\n * ```\n *\n * And the `.env.local` file:\n *\n * ```sh title=\".env.local\"\n * AUTH_GITHUB_ID=...\n * AUTH_GITHUB_SECRET=...\n * AUTH_SECRET=...\n * ```\n *\n * :::tip\n * In production, `AUTH_SECRET` is a required environment variable - if not set, NextAuth.js will throw an error. See [MissingSecretError](https://authjs.dev/reference/core/errors#missingsecret) for more details.\n * :::\n *\n * If you need to override the default values for a provider, you can still call it as a function `GitHub({...})` as before.\n *\n * ## Lazy initialization\n * You can also initialize NextAuth.js lazily (previously known as advanced intialization), which allows you to access the request context in the configuration in some cases, like Route Handlers, Middleware, API Routes or `getServerSideProps`.\n * The above example becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth(req => {\n *  if (req) {\n *   console.log(req) // do something with the request\n *  }\n *  return { providers: [ GitHub ] }\n * })\n * ```\n *\n * :::tip\n * This is useful if you want to customize the configuration based on the request, for example, to add a different provider in staging/dev environments.\n * :::\n *\n * @module next-auth\n */\n\n\n\n\n\n\n/**\n *  Initialize NextAuth.js.\n *\n *  @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth({ providers: [GitHub] })\n * ```\n *\n * Lazy initialization:\n *\n * @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth(async (req) => {\n *   console.log(req) // do something with the request\n *   return {\n *     providers: [GitHub],\n *   },\n * })\n * ```\n */\nfunction NextAuth(config) {\n    if (typeof config === \"function\") {\n        const httpHandler = async (req) => {\n            const _config = await config(req);\n            (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n            return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), _config);\n        };\n        return {\n            handlers: { GET: httpHandler, POST: httpHandler },\n            // @ts-expect-error\n            auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config, (c) => (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(c)),\n            signIn: async (provider, options, authorizationParams) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, _config);\n            },\n            signOut: async (options) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, _config);\n            },\n            unstable_update: async (data) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, _config);\n            },\n        };\n    }\n    (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(config);\n    const httpHandler = (req) => (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), config);\n    return {\n        handlers: { GET: httpHandler, POST: httpHandler },\n        // @ts-expect-error\n        auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config),\n        signIn: (provider, options, authorizationParams) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, config);\n        },\n        signOut: (options) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, config);\n        },\n        unstable_update: (data) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, config);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/actions.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/lib/actions.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   update: () => (/* binding */ update)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\nasync function signIn(provider, options = {}, authorizationParams, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    const { redirect: shouldRedirect = true, redirectTo, ...rest } = options instanceof FormData ? Object.fromEntries(options) : options;\n    const callbackUrl = redirectTo?.toString() ?? headers.get(\"Referer\") ?? \"/\";\n    const signInURL = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signin\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    if (!provider) {\n        signInURL.searchParams.append(\"callbackUrl\", callbackUrl);\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(signInURL.toString());\n        return signInURL.toString();\n    }\n    let url = `${signInURL}/${provider}?${new URLSearchParams(authorizationParams)}`;\n    let foundProvider = {};\n    for (const providerConfig of config.providers) {\n        const { options, ...defaults } = typeof providerConfig === \"function\" ? providerConfig() : providerConfig;\n        const id = options?.id ?? defaults.id;\n        if (id === provider) {\n            foundProvider = {\n                id,\n                type: options?.type ?? defaults.type,\n            };\n            break;\n        }\n    }\n    if (!foundProvider.id) {\n        const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(url);\n        return url;\n    }\n    if (foundProvider.type === \"credentials\") {\n        url = url.replace(\"signin\", \"callback\");\n    }\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const body = new URLSearchParams({ ...rest, callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    const responseUrl = res instanceof Response ? res.headers.get(\"Location\") : res.redirect;\n    // NOTE: if for some unexpected reason the responseUrl is not set,\n    // we redirect to the original url\n    const redirectUrl = responseUrl ?? url;\n    if (shouldRedirect)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(redirectUrl);\n    return redirectUrl;\n}\nasync function signOut(options, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signout\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const callbackUrl = options?.redirectTo ?? headers.get(\"Referer\") ?? \"/\";\n    const body = new URLSearchParams({ callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    if (options?.redirect ?? true)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(res.redirect);\n    return res;\n}\nasync function update(data, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/json\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const body = JSON.stringify({ data });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    return res.body;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/actions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/env.js":
/*!*******************************************!*\
  !*** ./node_modules/next-auth/lib/env.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reqWithEnvURL: () => (/* binding */ reqWithEnvURL),\n/* harmony export */   setEnvDefaults: () => (/* binding */ setEnvDefaults)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\n/** If `NEXTAUTH_URL` or `AUTH_URL` is defined, override the request's URL. */\nfunction reqWithEnvURL(req) {\n    const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n    if (!url)\n        return req;\n    const { origin: envOrigin } = new URL(url);\n    const { href, origin } = req.nextUrl;\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(href.replace(origin, envOrigin), req);\n}\n/**\n * For backwards compatibility, `next-auth` checks for `NEXTAUTH_URL`\n * and the `basePath` by default is `/api/auth` instead of `/auth`\n * (which is the default for all other Auth.js integrations).\n *\n * For the same reason, `NEXTAUTH_SECRET` is also checked.\n */\nfunction setEnvDefaults(config) {\n    try {\n        config.secret ?? (config.secret = process.env.AUTH_SECRET ?? process.env.NEXTAUTH_SECRET);\n        const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n        if (!url)\n            return;\n        const { pathname } = new URL(url);\n        if (pathname === \"/\")\n            return;\n        config.basePath || (config.basePath = pathname);\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/api/auth` below.\n    }\n    finally {\n        config.basePath || (config.basePath = \"/api/auth\");\n        (0,_auth_core__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(process.env, config, true);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/lib/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initAuth: () => (/* binding */ initAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _env_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./env.js */ \"(rsc)/./node_modules/next-auth/lib/env.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\nasync function getSession(headers, config) {\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const request = new Request(url, {\n        headers: { cookie: headers.get(\"cookie\") ?? \"\" },\n    });\n    return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(request, {\n        ...config,\n        callbacks: {\n            ...config.callbacks,\n            // Since we are server-side, we don't need to filter out the session data\n            // See https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n            // TODO: Taint the session data to prevent accidental leakage to the client\n            // https://react.dev/reference/react/experimental_taintObjectReference\n            async session(...args) {\n                const session = \n                // If the user defined a custom session callback, use that instead\n                (await config.callbacks?.session?.(...args)) ?? {\n                    ...args[0].session,\n                    expires: args[0].session.expires?.toISOString?.() ??\n                        args[0].session.expires,\n                };\n                const user = args[0].user ?? args[0].token;\n                return { user, ...session };\n            },\n        },\n    });\n}\nfunction isReqWrapper(arg) {\n    return typeof arg === \"function\";\n}\nfunction initAuth(config, onLazyLoad // To set the default env vars\n) {\n    if (typeof config === \"function\") {\n        return async (...args) => {\n            if (!args.length) {\n                // React Server Components\n                const _headers = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n                const _config = await config(undefined); // Review: Should we pass headers() here instead?\n                onLazyLoad?.(_config);\n                return getSession(_headers, _config).then((r) => r.json());\n            }\n            if (args[0] instanceof Request) {\n                // middleware.ts inline\n                // export { auth as default } from \"auth\"\n                const req = args[0];\n                const ev = args[1];\n                const _config = await config(req);\n                onLazyLoad?.(_config);\n                // args[0] is supposed to be NextRequest but the instanceof check is failing.\n                return handleAuth([req, ev], _config);\n            }\n            if (isReqWrapper(args[0])) {\n                // middleware.ts wrapper/route.ts\n                // import { auth } from \"auth\"\n                // export default auth((req) => { console.log(req.auth) }})\n                const userMiddlewareOrRoute = args[0];\n                return async (...args) => {\n                    const _config = await config(args[0]);\n                    onLazyLoad?.(_config);\n                    return handleAuth(args, _config, userMiddlewareOrRoute);\n                };\n            }\n            // API Routes, getServerSideProps\n            const request = \"req\" in args[0] ? args[0].req : args[0];\n            const response = \"res\" in args[0] ? args[0].res : args[1];\n            const _config = await config(request);\n            onLazyLoad?.(_config);\n            // @ts-expect-error -- request is NextRequest\n            return getSession(new Headers(request.headers), _config).then(async (authResponse) => {\n                const auth = await authResponse.json();\n                for (const cookie of authResponse.headers.getSetCookie())\n                    if (\"headers\" in response)\n                        response.headers.append(\"set-cookie\", cookie);\n                    else\n                        response.appendHeader(\"set-cookie\", cookie);\n                return auth;\n            });\n        };\n    }\n    return (...args) => {\n        if (!args.length) {\n            // React Server Components\n            return Promise.resolve((0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)()).then((h) => getSession(h, config).then((r) => r.json()));\n        }\n        if (args[0] instanceof Request) {\n            // middleware.ts inline\n            // export { auth as default } from \"auth\"\n            const req = args[0];\n            const ev = args[1];\n            return handleAuth([req, ev], config);\n        }\n        if (isReqWrapper(args[0])) {\n            // middleware.ts wrapper/route.ts\n            // import { auth } from \"auth\"\n            // export default auth((req) => { console.log(req.auth) }})\n            const userMiddlewareOrRoute = args[0];\n            return async (...args) => {\n                return handleAuth(args, config, userMiddlewareOrRoute).then((res) => {\n                    return res;\n                });\n            };\n        }\n        // API Routes, getServerSideProps\n        const request = \"req\" in args[0] ? args[0].req : args[0];\n        const response = \"res\" in args[0] ? args[0].res : args[1];\n        return getSession(\n        // @ts-expect-error\n        new Headers(request.headers), config).then(async (authResponse) => {\n            const auth = await authResponse.json();\n            for (const cookie of authResponse.headers.getSetCookie())\n                if (\"headers\" in response)\n                    response.headers.append(\"set-cookie\", cookie);\n                else\n                    response.appendHeader(\"set-cookie\", cookie);\n            return auth;\n        });\n    };\n}\nasync function handleAuth(args, config, userMiddlewareOrRoute) {\n    const request = (0,_env_js__WEBPACK_IMPORTED_MODULE_3__.reqWithEnvURL)(args[0]);\n    const sessionResponse = await getSession(request.headers, config);\n    const auth = await sessionResponse.json();\n    let authorized = true;\n    if (config.callbacks?.authorized) {\n        authorized = await config.callbacks.authorized({ request, auth });\n    }\n    let response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next?.();\n    if (authorized instanceof Response) {\n        // User returned a custom response, like redirecting to a page or 401, respect it\n        response = authorized;\n        const redirect = authorized.headers.get(\"Location\");\n        const { pathname } = request.nextUrl;\n        // If the user is redirecting to the same NextAuth.js action path as the current request,\n        // don't allow the redirect to prevent an infinite loop\n        if (redirect &&\n            isSameAuthAction(pathname, new URL(redirect).pathname, config)) {\n            authorized = true;\n        }\n    }\n    else if (userMiddlewareOrRoute) {\n        // Execute user's middleware/handler with the augmented request\n        const augmentedReq = request;\n        augmentedReq.auth = auth;\n        response =\n            (await userMiddlewareOrRoute(augmentedReq, args[1])) ??\n                next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next();\n    }\n    else if (!authorized) {\n        const signInPage = config.pages?.signIn ?? `${config.basePath}/signin`;\n        if (request.nextUrl.pathname !== signInPage) {\n            // Redirect to signin page by default if not authorized\n            const signInUrl = request.nextUrl.clone();\n            signInUrl.pathname = signInPage;\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.href);\n            response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(signInUrl);\n        }\n    }\n    const finalResponse = new Response(response?.body, response);\n    // Preserve cookies from the session response\n    for (const cookie of sessionResponse.headers.getSetCookie())\n        finalResponse.headers.append(\"set-cookie\", cookie);\n    return finalResponse;\n}\nfunction isSameAuthAction(requestPath, redirectPath, config) {\n    const action = redirectPath.replace(`${requestPath}/`, \"\");\n    const pages = Object.values(config.pages ?? {});\n    return ((actions.has(action) || pages.includes(redirectPath)) &&\n        redirectPath === requestPath);\n}\nconst actions = new Set([\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/github.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/github.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_github__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_github__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/github */ \"(rsc)/./node_modules/@auth/core/providers/github.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9naXRodWIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEM7QUFDVSIsInNvdXJjZXMiOlsiRDpcXFNvZnR3YXJlc1xcQWkgYm90XFxpbnR2aWV3LWFpXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxccHJvdmlkZXJzXFxnaXRodWIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2dpdGh1YlwiO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gXCJAYXV0aC9jb3JlL3Byb3ZpZGVycy9naXRodWJcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/github.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/google.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/google.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/google */ \"(rsc)/./node_modules/@auth/core/providers/google.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEM7QUFDVSIsInNvdXJjZXMiOlsiRDpcXFNvZnR3YXJlc1xcQWkgYm90XFxpbnR2aWV3LWFpXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxccHJvdmlkZXJzXFxnb29nbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2dvb2dsZVwiO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gXCJAYXV0aC9jb3JlL3Byb3ZpZGVycy9nb29nbGVcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/google.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n\nconst __NEXTAUTH = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call __NEXTAUTH() from the server but __NEXTAUTH is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"__NEXTAUTH\",\n);const SessionContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call SessionContext() from the server but SessionContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"SessionContext\",\n);const useSession = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"useSession\",\n);const getSession = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getSession() from the server but getSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getSession\",\n);const getCsrfToken = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getCsrfToken() from the server but getCsrfToken is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getCsrfToken\",\n);const getProviders = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getProviders() from the server but getProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getProviders\",\n);const signIn = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call signIn() from the server but signIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"signIn\",\n);const signOut = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call signOut() from the server but signOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"signOut\",\n);const SessionProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\node_modules\\\\next-auth\\\\react.js\",\n\"SessionProvider\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(ssr)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ \n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...req?.headers?.cookie ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (true) {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/lib/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(ssr)/./node_modules/next-auth/lib/client.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ \n\n\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\n// https://github.com/nextauthjs/next-auth/pull/10762\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{},\n            name: \"next-auth\",\n            onmessage: null,\n            onmessageerror: null,\n            close: ()=>{},\n            dispatchEvent: ()=>false\n        };\n    }\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                })}`;\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        // https://github.com/nextauthjs/next-auth/pull/11470\n        getNewBroadcastChannel().postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\nasync function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\nasync function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = `${baseUrl}/error`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo\n        })}`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            `Provider id \"${provider}\" refers to a WebAuthn provider.`,\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.'\n        ].join(\"\\n\"));\n    }\n    const signInUrl = `${baseUrl}/${providerType === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\nasync function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href } = options ?? {};\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async ({ event } = {})=>{\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react.js\n");

/***/ })

};
;