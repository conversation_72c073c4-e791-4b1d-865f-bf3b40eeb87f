exports.id=597,exports.ids=[597],exports.modules={25:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},1279:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var s=t(687);t(3210);var a=t(218);let o=({children:e,...r})=>(0,s.jsx)(a.N,{...r,children:e})},2704:()=>{},2910:(e,r,t)=>{Promise.resolve().then(t.bind(t,363)),Promise.resolve().then(t.bind(t,25)),Promise.resolve().then(t.bind(t,2175))},2925:(e,r,t)=>{"use strict";t.d(r,{lV:()=>c,MJ:()=>v,zB:()=>u,eI:()=>x,lR:()=>h,C5:()=>f});var s=t(687),a=t(3210),o=t(1391),n=t(7605),i=t(6241),d=t(9467);function l({className:e,...r}){return(0,s.jsx)(d.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}let c=n.Op,m=a.createContext({}),u=({...e})=>(0,s.jsx)(m.Provider,{value:{name:e.name},children:(0,s.jsx)(n.xI,{...e})}),p=()=>{let e=a.useContext(m),r=a.useContext(g),{getFieldState:t}=(0,n.xW)(),s=(0,n.lN)({name:e.name}),o=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...o}},g=a.createContext({});function x({className:e,...r}){let t=a.useId();return(0,s.jsx)(g.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-3 ",e),...r})})}function h({className:e,...r}){let{error:t,formItemId:a}=p();return(0,s.jsx)(l,{"data-slot":"form-label","data-error":!!t,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...r})}function v({...e}){let{error:r,formItemId:t,formDescriptionId:a,formMessageId:n}=p();return(0,s.jsx)(o.DX,{"data-slot":"form-control",id:t,"aria-describedby":r?`${a} ${n}`:`${a}`,"aria-invalid":!!r,...e})}function f({className:e,...r}){let{error:t,formMessageId:a}=p(),o=t?String(t?.message??""):r.children;return o?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,i.cn)("text-sm !text-red-600",e),...r,children:o}):null}},4013:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},4593:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>n});var s=t(687),a=t(218),o=t(2581);let n=({...e})=>{let{theme:r="system"}=(0,a.D)();return(0,s.jsx)(o.l$,{theme:r,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},4934:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(687);t(3210);var a=t(1391),o=t(4224),n=t(6241);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:o=!1,...d}){let l=o?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(i({variant:r,size:t,className:e})),...d})}},5958:(e,r,t)=>{Promise.resolve().then(t.bind(t,4593)),Promise.resolve().then(t.bind(t,1279)),Promise.resolve().then(t.bind(t,9208))},6241:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var s=t(9384),a=t(2348);function o(...e){return(0,a.QP)((0,s.$)(e))}},6487:()=>{},6749:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},6814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>n,j2:()=>l});var s=t(9859),a=t(3560),o=t(6056);let{handlers:n,signIn:i,signOut:d,auth:l}=(0,s.Ay)({providers:[a.A,o.A],secret:process.env.AUTH_SECRET})},7470:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(7413);let a=({children:e})=>(0,s.jsx)("main",{children:e})},8014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,metadata:()=>u});var s=t(7413),a=t(363),o=t(6649),n=t.n(o),i=t(5843),d=t.n(i);t(2704);var l=t(25),c=t(2175),m=t(6814);let u={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}},p=async({children:e})=>{let r=await (0,m.j2)();return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)(c.SessionProvider,{session:r,children:(0,s.jsxs)("body",{className:`${n().className} ${d().variable} antialiased`,children:[(0,s.jsx)(l.default,{attribute:"class",defaultTheme:"light",children:e}),(0,s.jsx)(a.Toaster,{})]})})})}},8335:()=>{},8988:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(687);t(3210);var a=t(6241);function o({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},9104:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(687),a=t(3210),o=t(7605),n=t(3442),i=t(6189),d=t(474),l=t(2925),c=t(8988),m=t(4934);let u=({mode:e,schema:r,defaultValues:t,onSubmit:u})=>{let p=(0,i.useRouter)(),g=(0,o.mN)({resolver:(0,n.u)(r),defaultValues:t,mode:"onChange"}),x=(0,a.useRef)(null),h=(0,a.useRef)(null),v=[x,h,(0,a.useRef)(null),(0,a.useRef)(null)],[f,b]=(0,a.useState)(!1),w=async r=>{(await u(r)).success&&("forgot"===e?p.push("/sign-in/Forgotpassword/verifyotp"):"otp"===e?p.push("/sign-in/Forgotpassword/verifyotp/Setnewpassword"):"reset"===e&&p.push("/sign-in"))},P=(e,r)=>{let t=r.target.value;g.setValue(`otp${e+1}`,t),t&&e<v.length-1&&v[e+1].current?.focus()};return(0,s.jsxs)("div",{className:"w-full px-4 py-6 lg:max-w-md lg:min-w-[28rem]",children:[(0,s.jsx)("h1",{className:"text-[28px] lg:text-[34px] leading-[36px] lg:leading-[41px] font-semibold font-poppins text-[#100F14] mb-2",children:"forgot"===e?"Forget Password":"reset"===e?"Reset Password":"Verification Code"}),(0,s.jsx)("p",{className:"text-gray-600 font-medium mb-8 text-poppins",children:"forgot"===e?"Enter your email id to request a password reset.":"reset"===e?"Please create a new password":"Enter OTP sent to your email for the verification process."}),(0,s.jsx)(l.lV,{...g,children:(0,s.jsxs)("form",{onSubmit:g.handleSubmit(w),className:"space-y-6",children:["otp"===e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h4",{className:"text-lg font-medium mb-3",children:"OTP"}),(0,s.jsx)("div",{className:"flex gap-4 justify-between",children:v.map((e,r)=>(0,s.jsx)(l.zB,{control:g.control,name:`otp${r+1}`,render:({field:e})=>{let{ref:t,onChange:a,...o}=e;return(0,s.jsxs)(l.eI,{className:"flex-1",children:[(0,s.jsx)(l.MJ,{children:(0,s.jsx)(c.p,{ref:e=>{t(e),v[r].current=e},maxLength:1,className:"text-center font-bold text-3xl py-8 sm:py-7",type:"text",inputMode:"numeric",pattern:"[0-9]*",onChange:e=>{a(e),P(r,e)},...o})}),(0,s.jsx)(l.C5,{})]})}},r))})]}):Object.keys(t).map(e=>(0,s.jsx)(l.zB,{control:g.control,name:e,render:({field:r})=>(0,s.jsxs)(l.eI,{children:[(0,s.jsx)(l.lR,{className:"text-[#2E2E2E]",children:"email"===e?"Email Address":"password"===e||"newPassword"===e?"Password":"Confirm Password"}),(0,s.jsx)(l.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c.p,{type:e.toLowerCase().includes("password")&&!f?"password":"text",placeholder:"email"===e?"Enter Your Email address":"password"===e||"newPassword"===e?"Enter Your Password":"Confirm Your Password",className:"py-5 sm:py-6 pr-12",...r}),e.toLowerCase().includes("password")&&(0,s.jsx)("button",{type:"button",onClick:()=>b(e=>!e),className:"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer",children:(0,s.jsx)(d.default,{src:"/images/eye.png",alt:"Toggle Password Visibility",width:20,height:20})})]})}),(0,s.jsx)(l.C5,{})]})},e)),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(m.$,{type:"submit",className:"lg:w-[360px] w-full  bg-[#6938EF] hover:bg-[#682ed6] hover:cursor-pointer text-white py-5 sm:py-6 px-4 rounded-full mt-5",disabled:g.formState.isSubmitting||!g.formState.isValid,children:g.formState.isSubmitting?"forgot"===e?"Sending...":"reset"===e?"Resetting...":"Verifying...":"forgot"===e?"Continue":"reset"===e?"Reset Password":"Continue"})})]})})]})}},9360:(e,r,t)=>{"use strict";t.d(r,{Ih:()=>n,Il:()=>a,mJ:()=>o,pi:()=>d,zL:()=>i});var s=t(7566);let a=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")}),o=s.Ik({confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),name:s.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")}),n=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."})}),i=s.Ik({newPassword:s.Yj().min(6,{message:"Password must be at least 6 characters long"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Password do not match",path:["confirmPassword"]}),d=s.Ik({otp1:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp2:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp3:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp4:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed")})}};