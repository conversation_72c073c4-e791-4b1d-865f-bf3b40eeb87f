{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/interview", "regex": "^/interview(?:/)?$", "routeKeys": {}, "namedRegex": "^/interview(?:/)?$"}, {"page": "/job-posts", "regex": "^/job\\-posts(?:/)?$", "routeKeys": {}, "namedRegex": "^/job\\-posts(?:/)?$"}, {"page": "/sign-in", "regex": "^/sign\\-in(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-in(?:/)?$"}, {"page": "/sign-in/Forgotpassword", "regex": "^/sign\\-in/Forgotpassword(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-in/Forgotpassword(?:/)?$"}, {"page": "/sign-in/Forgotpassword/verifyotp", "regex": "^/sign\\-in/Forgotpassword/verifyotp(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-in/Forgotpassword/verifyotp(?:/)?$"}, {"page": "/sign-in/Forgotpassword/verifyotp/Setnewpassword", "regex": "^/sign\\-in/Forgotpassword/verifyotp/Setnewpassword(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-in/Forgotpassword/verifyotp/Setnewpassword(?:/)?$"}, {"page": "/sign-up", "regex": "^/sign\\-up(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-up(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}