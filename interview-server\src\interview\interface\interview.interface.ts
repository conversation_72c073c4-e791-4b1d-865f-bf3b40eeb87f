export interface ConversationMessage {
  role: 'candidate' | 'interviewer';
  content: string;
}

export interface InterviewRequest {
  position: string;
  name: string;
  experience: number;
  history: ConversationMessage[];
}

export interface ScoreCard {
  technicalSkills: number;
  problemSolving: number;
  communication: number;
  experience: number;
  overall: number;
}

export interface Summary {
  ScoreCard: ScoreCard;
  recommendation: string;
  reason: string;
}

export interface VideoScores {
  professionalism: number;
  energyLevel: number;
  communication: number;
  sociability: number;
}

export interface InterviewResponse {
  nextQuestion: string;
  currentQuestionScore: number;
  isInterviewCompleted: boolean;
  Summary?: Summary;
}