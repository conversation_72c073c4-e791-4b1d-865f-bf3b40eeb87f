(()=>{var e={};e.id=212,e.ids=[212],e.modules={497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(687);r(3210);var i=r(9104),o=r(9360),n=r(474);let a=()=>{let e={otp1:"",otp2:"",otp3:"",otp4:""},t=async e=>(console.log("OTP Submitted:",e),{success:!0});return(0,s.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,s.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,s.jsx)(n.default,{src:"/images/bgauthimg.png",alt:"auth",fill:!0,className:"object-cover",priority:!0})}),(0,s.jsxs)("div",{className:"lg:hidden",children:[(0,s.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,s.jsx)(n.default,{src:"/images/bgauthimg.png",alt:"auth",fill:!0,className:"object-cover",priority:!0})}),(0,s.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,s.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,s.jsx)(i.A,{mode:"otp",schema:o.pi,defaultValues:e,onSubmit:t})})})]}),(0,s.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,s.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,s.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,s.jsx)("div",{className:"w-full max-w-md",children:(0,s.jsx)(i.A,{mode:"otp",schema:o.pi,defaultValues:e,onSubmit:t})})})})})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2477:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>d});var s=r(5239),i=r(8088),o=r(8170),n=r.n(o),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["(auth)",{children:["sign-in",{children:["Forgotpassword",{children:["verifyotp",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6007)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\Forgotpassword\\verifyotp\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,7470)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\Forgotpassword\\verifyotp\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(auth)/sign-in/Forgotpassword/verifyotp/page",pathname:"/sign-in/Forgotpassword/verifyotp",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3289:(e,t,r)=>{Promise.resolve().then(r.bind(r,6007))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5511:e=>{"use strict";e.exports=require("crypto")},6007:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(auth)\\\\sign-in\\\\Forgotpassword\\\\verifyotp\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\Forgotpassword\\verifyotp\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9737:(e,t,r)=>{Promise.resolve().then(r.bind(r,497))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[97,423,598,23,937,597],()=>r(2477));module.exports=s})();