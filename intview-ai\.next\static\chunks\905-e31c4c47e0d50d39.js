"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[905],{3582:(e,r,a)=>{a.d(r,{Ih:()=>o,Il:()=>t,mJ:()=>n,pi:()=>d,zL:()=>i});var s=a(8309);let t=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")}),n=s.Ik({confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),name:s.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")}),o=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."})}),i=s.Ik({newPassword:s.Yj().min(6,{message:"Password must be at least 6 characters long"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Password do not match",path:["confirmPassword"]}),d=s.Ik({otp1:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp2:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp3:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp4:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed")})},3999:(e,r,a)=>{a.d(r,{cn:()=>n});var s=a(2596),t=a(9688);function n(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,t.QP)((0,s.$)(r))}},5318:(e,r,a)=>{a.d(r,{A:()=>m});var s=a(5155),t=a(2115),n=a(2177),o=a(221),i=a(5695),d=a(6766),l=a(9540),c=a(9852),u=a(7168);let m=e=>{let{mode:r,schema:a,defaultValues:m,onSubmit:p}=e,g=(0,i.useRouter)(),x=(0,n.mN)({resolver:(0,o.u)(a),defaultValues:m,mode:"onChange"}),f=(0,t.useRef)(null),h=(0,t.useRef)(null),w=[f,h,(0,t.useRef)(null),(0,t.useRef)(null)],[v,b]=(0,t.useState)(!1),j=async e=>{(await p(e)).success&&("forgot"===r?g.push("/sign-in/Forgotpassword/verifyotp"):"otp"===r?g.push("/sign-in/Forgotpassword/verifyotp/Setnewpassword"):"reset"===r&&g.push("/sign-in"))},P=(e,r)=>{let a=r.target.value;if(x.setValue("otp".concat(e+1),a),a&&e<w.length-1){var s;null==(s=w[e+1].current)||s.focus()}};return(0,s.jsxs)("div",{className:"w-full px-4 py-6 lg:max-w-md lg:min-w-[28rem]",children:[(0,s.jsx)("h1",{className:"text-[28px] lg:text-[34px] leading-[36px] lg:leading-[41px] font-semibold font-poppins text-[#100F14] mb-2",children:"forgot"===r?"Forget Password":"reset"===r?"Reset Password":"Verification Code"}),(0,s.jsx)("p",{className:"text-gray-600 font-medium mb-8 text-poppins",children:"forgot"===r?"Enter your email id to request a password reset.":"reset"===r?"Please create a new password":"Enter OTP sent to your email for the verification process."}),(0,s.jsx)(l.lV,{...x,children:(0,s.jsxs)("form",{onSubmit:x.handleSubmit(j),className:"space-y-6",children:["otp"===r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h4",{className:"text-lg font-medium mb-3",children:"OTP"}),(0,s.jsx)("div",{className:"flex gap-4 justify-between",children:w.map((e,r)=>(0,s.jsx)(l.zB,{control:x.control,name:"otp".concat(r+1),render:e=>{let{field:a}=e,{ref:t,onChange:n,...o}=a;return(0,s.jsxs)(l.eI,{className:"flex-1",children:[(0,s.jsx)(l.MJ,{children:(0,s.jsx)(c.p,{ref:e=>{t(e),w[r].current=e},maxLength:1,className:"text-center font-bold text-3xl py-8 sm:py-7",type:"text",inputMode:"numeric",pattern:"[0-9]*",onChange:e=>{n(e),P(r,e)},...o})}),(0,s.jsx)(l.C5,{})]})}},r))})]}):Object.keys(m).map(e=>(0,s.jsx)(l.zB,{control:x.control,name:e,render:r=>{let{field:a}=r;return(0,s.jsxs)(l.eI,{children:[(0,s.jsx)(l.lR,{className:"text-[#2E2E2E]",children:"email"===e?"Email Address":"password"===e||"newPassword"===e?"Password":"Confirm Password"}),(0,s.jsx)(l.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c.p,{type:e.toLowerCase().includes("password")&&!v?"password":"text",placeholder:"email"===e?"Enter Your Email address":"password"===e||"newPassword"===e?"Enter Your Password":"Confirm Your Password",className:"py-5 sm:py-6 pr-12",...a}),e.toLowerCase().includes("password")&&(0,s.jsx)("button",{type:"button",onClick:()=>b(e=>!e),className:"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer",children:(0,s.jsx)(d.default,{src:"/images/eye.png",alt:"Toggle Password Visibility",width:20,height:20})})]})}),(0,s.jsx)(l.C5,{})]})}},e)),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(u.$,{type:"submit",className:"lg:w-[360px] w-full  bg-[#6938EF] hover:bg-[#682ed6] hover:cursor-pointer text-white py-5 sm:py-6 px-4 rounded-full mt-5",disabled:x.formState.isSubmitting||!x.formState.isValid,children:x.formState.isSubmitting?"forgot"===r?"Sending...":"reset"===r?"Resetting...":"Verifying...":"forgot"===r?"Continue":"reset"===r?"Reset Password":"Continue"})})]})})]})}},7168:(e,r,a)=>{a.d(r,{$:()=>d});var s=a(5155);a(2115);var t=a(4624),n=a(2085),o=a(3999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:a,size:n,asChild:d=!1,...l}=e,c=d?t.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:a,size:n,className:r})),...l})}},9540:(e,r,a)=>{a.d(r,{lV:()=>c,MJ:()=>h,zB:()=>m,eI:()=>x,lR:()=>f,C5:()=>w});var s=a(5155),t=a(2115),n=a(4624),o=a(2177),i=a(3999),d=a(7073);function l(e){let{className:r,...a}=e;return(0,s.jsx)(d.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...a})}let c=o.Op,u=t.createContext({}),m=e=>{let{...r}=e;return(0,s.jsx)(u.Provider,{value:{name:r.name},children:(0,s.jsx)(o.xI,{...r})})},p=()=>{let e=t.useContext(u),r=t.useContext(g),{getFieldState:a}=(0,o.xW)(),s=(0,o.lN)({name:e.name}),n=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},g=t.createContext({});function x(e){let{className:r,...a}=e,n=t.useId();return(0,s.jsx)(g.Provider,{value:{id:n},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-3 ",r),...a})})}function f(e){let{className:r,...a}=e,{error:t,formItemId:n}=p();return(0,s.jsx)(l,{"data-slot":"form-label","data-error":!!t,className:(0,i.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...a})}function h(e){let{...r}=e,{error:a,formItemId:t,formDescriptionId:o,formMessageId:i}=p();return(0,s.jsx)(n.DX,{"data-slot":"form-control",id:t,"aria-describedby":a?"".concat(o," ").concat(i):"".concat(o),"aria-invalid":!!a,...r})}function w(e){var r;let{className:a,...t}=e,{error:n,formMessageId:o}=p(),d=n?String(null!=(r=null==n?void 0:n.message)?r:""):t.children;return d?(0,s.jsx)("p",{"data-slot":"form-message",id:o,className:(0,i.cn)("text-sm !text-red-600",a),...t,children:d}):null}},9852:(e,r,a)=>{a.d(r,{p:()=>n});var s=a(5155);a(2115);var t=a(3999);function n(e){let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}}}]);