{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "MYmvHnEuBkyLoUa6hR-yU", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "176HBskgJYAnybH7LVyHHvxARlgEn3Aw6mdfIvsb7qs=", "__NEXT_PREVIEW_MODE_ID": "4c149d20937c580c150f9e913ce8717d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ed294aaf61d246377526c06dba6a78b48f1eec33a3cc2b69ec45e76acffac1f0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ee66202dc7178cdad3c4755136e8264c543422352f00041d50f2e39993f0fe2a"}}}, "functions": {}, "sortedMiddleware": ["/"]}