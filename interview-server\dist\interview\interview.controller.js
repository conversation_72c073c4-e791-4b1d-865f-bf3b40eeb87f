"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InterviewController = void 0;
const common_1 = require("@nestjs/common");
const interview_service_1 = require("./interview.service");
let InterviewController = class InterviewController {
    constructor(interviewService) {
        this.interviewService = interviewService;
    }
    async runInterview(interviewData) {
        try {
            if (!interviewData.position || !interviewData.name || interviewData.experience === undefined) {
                throw new common_1.HttpException('Missing required fields: position, name, and experience are required', common_1.HttpStatus.BAD_REQUEST);
            }
            if (!interviewData.history) {
                interviewData.history = [];
            }
            return await this.interviewService.runInterview(interviewData);
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            console.error('Interview API Error:', error);
            throw new common_1.HttpException('Internal server error while processing interview', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async analyzeVideo(data) {
        try {
            if (!data.transcript || !Array.isArray(data.transcript)) {
                throw new common_1.HttpException('Invalid transcript data: transcript must be an array', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.interviewService.analyzeVideoTranscript(data.transcript);
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            console.error('Video Analysis API Error:', error);
            throw new common_1.HttpException('Internal server error while analyzing video transcript', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.InterviewController = InterviewController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], InterviewController.prototype, "runInterview", null);
__decorate([
    (0, common_1.Post)('analyze-video'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], InterviewController.prototype, "analyzeVideo", null);
exports.InterviewController = InterviewController = __decorate([
    (0, common_1.Controller)('interview'),
    __metadata("design:paramtypes", [interview_service_1.InterviewService])
], InterviewController);
//# sourceMappingURL=interview.controller.js.map