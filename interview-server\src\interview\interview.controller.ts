import { <PERSON>, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { InterviewService } from './interview.service';
import { InterviewRequest, InterviewResponse, ConversationMessage } from './interface/interview.interface';

@Controller('interview')
export class InterviewController {
  constructor(private readonly interviewService: InterviewService) {}

  @Post()
  async runInterview(@Body() interviewData: InterviewRequest): Promise<InterviewResponse> {
    try {
      // Validate required fields
      if (!interviewData.position || !interviewData.name || interviewData.experience === undefined) {
        throw new HttpException(
          'Missing required fields: position, name, and experience are required',
          HttpStatus.BAD_REQUEST
        );
      }

      // Ensure history is an array (default to empty array if not provided)
      if (!interviewData.history) {
        interviewData.history = [];
      }

      return await this.interviewService.runInterview(interviewData);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      console.error('Interview API Error:', error);
      throw new HttpException(
        'Internal server error while processing interview',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('analyze-video')
  async analyzeVideo(@Body() data: { transcript: ConversationMessage[] }) {
    try {
      if (!data.transcript || !Array.isArray(data.transcript)) {
        throw new HttpException(
          'Invalid transcript data: transcript must be an array',
          HttpStatus.BAD_REQUEST
        );
      }

      return await this.interviewService.analyzeVideoTranscript(data.transcript);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      console.error('Video Analysis API Error:', error);
      throw new HttpException(
        'Internal server error while analyzing video transcript',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}