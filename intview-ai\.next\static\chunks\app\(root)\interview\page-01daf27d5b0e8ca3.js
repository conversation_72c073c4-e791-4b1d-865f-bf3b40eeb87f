(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[812],{3999:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var a=t(2596),r=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}},6149:(e,s,t)=>{Promise.resolve().then(t.bind(t,8475))},7168:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var a=t(5155);t(2115);var r=t(4624),i=t(2085),l=t(3999);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:t,size:i,asChild:c=!1,...o}=e,d=c?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,l.cn)(n({variant:t,size:i,className:s})),...o})}},8475:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>D});var a=t(5155),r=t(2115),i=t(7168),l=t(2138);let n=["The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned."],c=["To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face."],o=["Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .","AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .","Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .","Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer ."],d=e=>{let{candidateName:s="Jonathan",jobTitle:t="Insurance Agent",languages:d=["English","Chinese"],instructions:x=n,environmentChecklist:m=c,disclaimers:u=o,onNext:h}=e,[g,p]=(0,r.useState)(!1);return(0,a.jsx)("div",{className:"flex-1 border border-gray-400 rounded-md h-fit bg-white",children:(0,a.jsxs)("div",{className:"p-4 flex flex-col text-[#38383a]",children:[(0,a.jsx)("p",{className:"font-semibold mb-8 text-xl",children:"Instructions for Interview!"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:" mb-2 text-md",children:["Hello ",s,"!"]}),(0,a.jsxs)("p",{className:"text-sm mb-4",children:["As part of the process you are required to complete an AI video assessment for the role of the ",t,"."]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Interview Language"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:d.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Instructions"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:x.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Environment Checklist:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:m.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Important Disclaimers:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:u.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2 mt-6",children:[(0,a.jsx)("input",{type:"checkbox",id:"terms",checked:g,onChange:e=>p(e.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsxs)("label",{htmlFor:"terms",className:"text-[11px] text-[#38383a]",children:["By checking this box, you agree with AI Interview"," ",(0,a.jsx)("span",{className:"text-primary cursor-pointer font-medium",children:"Terms of use"}),"."]})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(i.$,{disabled:!g,variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>h&&h(),children:["Proceed",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})})};var x=t(646),m=t(9588),u=t(5133),h=t(4516),g=t(6325);let p=()=>(0,a.jsx)("div",{className:"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"UX/UI Designer for Ai-Interview Web App"}),(0,a.jsxs)("div",{className:"flex gap-2 leading-relaxed mb-3 flex-wrap",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600 font-medium",children:["$500 - $1000 ",(0,a.jsx)("span",{className:"font-extrabold px-1",children:"\xb7"})]}),(0,a.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,a.jsx)(h.A,{className:"w-4 h-5"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"New York"})]}),(0,a.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,a.jsx)(g.A,{className:"w-4 h-5"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Onsite / Remote"})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"We're building an AI-powered interview tool. We expect you to help users prepare by giving human interview experience generation."})]}),(0,a.jsx)("span",{className:"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium",children:"Active"})]})});class f{async sendInterviewRequest(e){try{let s=await fetch("".concat(this.baseUrl,"/interview"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.text();throw Error("Interview API Error: ".concat(s.status," ").concat(s.statusText," - ").concat(e))}return await s.json()}catch(e){throw console.error("Failed to send interview request:",e),e}}async startInterview(e,s,t){return await this.sendInterviewRequest({position:e,name:s,experience:t,history:[]})}async continueInterview(e,s,t,a){return await this.sendInterviewRequest({position:e,name:s,experience:t,history:a})}constructor(){this.baseUrl="https://interview-server-delta.vercel.app"}}let v=new f,j=(0,r.createContext)(void 0),b=e=>{let{children:s}=e,[t,i]=(0,r.useState)(""),[l,n]=(0,r.useState)(!1),[c,o]=(0,r.useState)(!1),[d,x]=(0,r.useState)("Anas ALi"),[m,u]=(0,r.useState)("Software Engineer"),[h,g]=(0,r.useState)(3),[p,f]=(0,r.useState)(0),[b,w]=(0,r.useState)(0),[y,N]=(0,r.useState)(0),[A,S]=(0,r.useState)([]),[k,I]=(0,r.useState)(!1),[C,E]=(0,r.useState)(null),[F,q]=(0,r.useState)([]),[T,R]=(0,r.useState)(null),z=(0,r.useCallback)(e=>{q(s=>[...s,e])},[]),P=(0,r.useCallback)(async()=>{o(!0),R(null);try{let e=await v.startInterview(m,d,h);i(e.nextQuestion),f(e.currentQuestionScore),I(e.isInterviewCompleted),e.Summary&&E(e.Summary),z({role:"interviewer",content:e.nextQuestion}),n(!0)}catch(e){R(e instanceof Error?e.message:"Failed to start interview"),console.error("Failed to start interview:",e)}finally{o(!1)}},[m,d,h,z]),Q=(0,r.useCallback)(async e=>{o(!0),R(null);try{let s={role:"candidate",content:e},t=[...F,s];z(s);let a=await v.continueInterview(m,d,h,t);i(a.nextQuestion),f(a.currentQuestionScore),I(a.isInterviewCompleted),a.currentQuestionScore>0&&(w(e=>e+a.currentQuestionScore),N(e=>e+1),S(e=>[...e,a.currentQuestionScore])),a.Summary&&E(a.Summary),!a.isInterviewCompleted&&a.nextQuestion&&z({role:"interviewer",content:a.nextQuestion})}catch(e){R(e instanceof Error?e.message:"Failed to submit answer"),console.error("Failed to submit answer:",e)}finally{o(!1)}},[m,d,h,F,z]);return(0,a.jsx)(j.Provider,{value:{currentQuestion:t,setCurrentQuestion:i,isInterviewStarted:l,setIsInterviewStarted:n,isLoading:c,setIsLoading:o,candidateName:d,setCandidateName:x,jobTitle:m,setJobTitle:u,experience:h,setExperience:g,currentQuestionScore:p,totalScore:b,questionCount:y,questionScores:A,isInterviewCompleted:k,interviewSummary:C,conversationHistory:F,addToHistory:z,startInterview:P,submitAnswer:Q,error:T,setError:R},children:s})},w=()=>{let e=(0,r.useContext)(j);if(void 0===e)throw Error("useInterview must be used within an InterviewProvider");return e},y=e=>{let{className:s}=e,{conversationHistory:t,isInterviewStarted:r}=w(),i=t.filter(e=>"interviewer"===e.role);return(0,a.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full shadow-sm overflow-y-auto scrollbar-hidden ".concat(s||""),children:[(0,a.jsx)("h3",{className:"font-semibold text-lg mb-6",children:"Video Transcript"}),r?0===i.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"Loading questions..."}):(0,a.jsx)("ul",{className:"space-y-4",children:i.map((e,s)=>(0,a.jsxs)("li",{className:"relative flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium bg-[#6938EF] text-white flex-shrink-0",children:s+1}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsx)("p",{className:"text-sm text-gray-800 leading-relaxed",children:e.content})})]},s))}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"Interview not started yet"})]})},N=e=>{let{children:s}=e;return(0,a.jsx)("div",{className:"border rounded-lg p-6 min-h-[600px] mb-4 flex-1",children:s})};var A=t(8979);t(5925);let S=null,k=null;try{let e=t(6866);S=e.default,k=e.useSpeechRecognition}catch(e){S=null,k=null}let I=e=>{let{onTranscriptChange:s,onFinalTranscript:t,isDisabled:i=!1,className:l=""}=e,[n,c]=(0,r.useState)(!1),[o,d]=(0,r.useState)(!1),{transcript:x,listening:u,resetTranscript:h,browserSupportsSpeechRecognition:g,isMicrophoneAvailable:p}=k();return((0,r.useEffect)(()=>{x&&s(x)},[x,s]),(0,r.useEffect)(()=>{o&&!u&&x&&(t(x),c(!1),d(!1))},[u,x,o,t]),(0,r.useEffect)(()=>{},[u,n]),g)?p?(0,a.jsxs)("div",{className:"space-y-4 ".concat(l),children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[!n&&(0,a.jsx)("div",{className:"bg-primary rounded-full p-4 text-white ",children:(0,a.jsx)(m.A,{className:"w-6 h-6  ",onClick:()=>{S&&g&&p&&!i&&(h(),c(!0),d(!0),S.startListening({continuous:!0,language:"en-US"}))}})}),n&&(0,a.jsxs)("div",{className:"relative flex items-center justify-center",children:[(0,a.jsx)("span",{className:"absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75 animate-ping"}),(0,a.jsx)("div",{className:"relative bg-red-400 rounded-full p-4 text-white",children:(0,a.jsx)(A.A,{className:"w-6 h-6",onClick:()=>{S&&(S.stopListening(),c(!1),x&&t(x),d(!1))}})})]})]}),x&&(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-gray-800 text-sm leading-relaxed",children:x})})]}):(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 ".concat(l),children:(0,a.jsx)("p",{className:"text-yellow-700 text-sm",children:"Microphone access is required for voice input. Please allow microphone permissions and refresh the page."})}):(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(l),children:(0,a.jsx)("p",{className:"text-red-700 text-sm",children:"Your browser doesn't support speech recognition. Please use a modern browser like Chrome, Edge, or Safari."})})},C=e=>{let{className:s=""}=e;return(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 ".concat(s),children:(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Speech recognition is not available in this environment."})})},E=e=>{let[s,t]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{t(!0)},[]),s)?k?(0,a.jsx)(I,{...e}):(0,a.jsx)(C,{...e}):(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 ".concat(e.className||""),children:(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Initializing voice recognition..."})})},F=e=>{let{onNext:s}=e,{currentQuestion:t,isInterviewStarted:n,isLoading:c,startInterview:o,submitAnswer:d,error:h,isInterviewCompleted:g,interviewSummary:f}=w(),[v,j]=(0,r.useState)(!1),[b,A]=(0,r.useState)(""),[S,k]=(0,r.useState)("voice"),[I,C]=(0,r.useState)(!1),F=async()=>{try{await o(),j(!0)}catch(e){console.error("Failed to start interview:",e)}},q=()=>{A(""),C(e=>!e)},T=()=>{k("voice"===S?"text":"voice"),A("")},R=async()=>{if(!b.trim())return void alert("Please provide an answer before continuing.");try{j(!1),await d(b),A(""),j(!0)}catch(e){console.error("Failed to submit answer:",e),j(!0)}};return n?g?(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(p,{}),(0,a.jsx)(N,{children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(x.A,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Interview Completed!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Thank you for completing the interview. Your responses have been recorded."}),f&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Detailed Interview Summary"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Technical Skills:"}),(0,a.jsxs)("span",{className:"font-medium",children:[f.ScoreCard.technicalSkills,"/20"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Problem Solving:"}),(0,a.jsxs)("span",{className:"font-medium",children:[f.ScoreCard.problemSolving,"/20"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Communication:"}),(0,a.jsxs)("span",{className:"font-medium",children:[f.ScoreCard.communication,"/20"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Experience:"}),(0,a.jsxs)("span",{className:"font-medium",children:[f.ScoreCard.experience,"/20"]})]}),(0,a.jsx)("hr",{className:"my-2"}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold text-base",children:[(0,a.jsx)("span",{children:"Total Score:"}),(0,a.jsxs)("span",{children:[f.ScoreCard.overall,"/100"]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("HIRE"===f.recommendation?"bg-green-100 text-green-800":"REJECT"===f.recommendation?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:f.recommendation})}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mt-2",children:f.reason})]})]})]})]}),(0,a.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>null==s?void 0:s(),children:["View Results",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})]})})]}):(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(p,{}),(0,a.jsxs)(N,{children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-10 justify-center items-center lg:items-start",children:[(0,a.jsxs)("div",{className:"flex-1 max-w-2xl",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Question:"}),(0,a.jsx)("p",{className:"text-gray-700 text-lg leading-relaxed",children:t||"Loading question..."}),h&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:["Error: ",h]})]}),v&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Your Answer:"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.$,{variant:"voice"===S?"default":"outline",size:"sm",onClick:T,className:"flex items-center gap-2 ".concat("voice"===S?"text-white":"bg-transparent border-gray-400 text-gray-400 hover:bg-gray-100"),children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Voice"]}),(0,a.jsxs)(i.$,{variant:"text"===S?"default":"outline",size:"sm",onClick:T,className:"flex items-center gap-2 ".concat("text"===S?"text-white":"bg-transparent border-gray-400 text-gray-400 hover:bg-gray-100"),children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),"Text"]}),b&&"voice"===S&&(0,a.jsx)(i.$,{onClick:q,variant:"outline",size:"sm",className:"flex items-center gap-2",children:"Clear"})]})]}),"voice"===S?(0,a.jsx)(E,{onTranscriptChange:e=>{A(e)},onFinalTranscript:e=>{A(e)},onClear:q,clearTrigger:I,isDisabled:c,className:"mb-4"}):(0,a.jsx)("textarea",{value:b,onChange:e=>A(e.target.value),placeholder:"Type your answer here...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4})]})]}),(0,a.jsx)(y,{className:"h-[400px] lg:w-80"})]}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:v&&!c?(0,a.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:R,children:[g?"Finish Interview":"Submit Answer",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]}):(0,a.jsx)("div",{className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500",children:c?"Loading question...":"Listen to the question"})})]})]}):(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(p,{}),(0,a.jsxs)(N,{children:[(0,a.jsx)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:(0,a.jsx)(y,{className:"h-[550px]"})}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,a.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:F,children:["Start Interview",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})}),(0,a.jsx)("div",{className:"flex justify-center mt-5 text-2xl font-semibold text-primary",children:"Ready to begin"})]})]})},q=()=>{let{conversationHistory:e,currentQuestionScore:s,totalScore:t}=w();return(0,a.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-5",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-black",children:"Interview Transcript"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Score: ",t,"/100"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[e.map((t,r)=>(0,a.jsx)("div",{className:"mb-4",children:"interviewer"===t.role?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm font-semibold text-blue-600 mb-1",children:["Question ",Math.floor(r/2)+1,":"]}),(0,a.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed",children:t.content})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-semibold text-green-600 mb-1",children:"Answer:"}),(0,a.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed",children:t.content}),r===e.length-1&&s>0&&(0,a.jsxs)("p",{className:"text-xs text-blue-500 mt-1",children:["Score: ",s,"/20"]})]})},r)),0===e.length&&(0,a.jsx)("p",{className:"text-sm text-gray-500 italic",children:"Interview transcript will appear here as you progress..."})]})]})},T=e=>{let{onNext:s}=e;return(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(p,{}),(0,a.jsxs)(N,{children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,a.jsx)(y,{}),(0,a.jsx)(q,{})]}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,a.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>s&&s(),children:["Finish Interview",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})};var R=t(6766);let z={src:"/_next/static/media/trophy.73528452.png",height:28,width:28,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEXNszH7qED//1HPmRGtnSfDoyrn1T1MaXHr1j+cqzrVpR7SoiDosSnluR7esSLrwyPptBvv0S75zPcvAAAAEnRSTlMR/hacHCkqADMIjM+nl9x4XaVFuRFRAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nBXFSRIAIQgAsUZBwN3/f3ZqcglJMSskhKpq/Pe9uwZWn8irRrtLZN0GkedkgLecM5vjzhi4fzkhAbtZdsbsAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},P=()=>(0,a.jsxs)("div",{className:"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30",children:[(0,a.jsx)("div",{className:"flex justify-center mb-2",children:(0,a.jsx)(R.default,{src:z,alt:"Trophy"})}),(0,a.jsx)("p",{className:"text-xl font-bold text-[#1E1E1E]",children:"55%"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Overall Score"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2",children:"AI Interviewer"}),(0,a.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"UI UX Designer"}),(0,a.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"18th June, 2025"})]})]}),(0,a.jsx)("div",{className:"top-0",children:(0,a.jsx)("span",{className:"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full",children:"Evaluated"})})]}),Q=e=>{let{label:s,value:t,color:r="bg-orange-500"}=e;return(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"mb-1",children:s}),(0,a.jsxs)("span",{children:[t,"/100"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,a.jsx)("div",{className:"h-2.5 rounded-full ".concat(r),style:{width:"".concat(t,"%")}})})]})};var V=t(6636);t(839);let M=e=>{let{label:s,percent:t,color:r,trailColor:i}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-1 mb-2",children:[(0,a.jsx)("p",{className:"text-sm font-semibold mb-3",children:s}),(0,a.jsx)("div",{className:"w-32 h-28",children:(0,a.jsx)(V.QF,{value:t,text:"".concat(t,"%"),strokeWidth:10,styles:(0,V.Hf)({textSize:"12px",pathColor:r,textColor:"#5a5a5a",trailColor:i})})})]})},L=()=>(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between font-semibold mb-4",children:[(0,a.jsx)("span",{children:"Resume Score"}),(0,a.jsx)("span",{children:"65%"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)(Q,{label:"Company Fit",value:66}),(0,a.jsx)(Q,{label:"Relevant Experience",value:66,color:"bg-purple-600"}),(0,a.jsx)(Q,{label:"Job Knowledge",value:66}),(0,a.jsx)(Q,{label:"Education",value:66}),(0,a.jsx)(Q,{label:"Hard Skills",value:66})]}),(0,a.jsxs)("div",{className:"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8",children:["Over All Score \xa0 ",(0,a.jsx)("span",{className:"text-black",children:"66/100"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsx)("div",{className:"font-semibold mb-4",children:"Video Score"}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)(Q,{label:"Professionalism",value:64}),(0,a.jsx)(Q,{label:"Energy Level",value:56,color:"bg-purple-600"}),(0,a.jsx)(Q,{label:"Communication",value:58}),(0,a.jsx)(Q,{label:"Sociability",value:70})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm",children:[(0,a.jsx)("p",{className:"font-semibold",children:"AI Rating"}),(0,a.jsx)(M,{label:"AI Resume Rating",percent:75,color:"#A855F7",trailColor:"#EAE2FF"}),(0,a.jsx)(M,{label:"AI Video Rating",percent:75,color:"#FF5B00",trailColor:"#FFEAE1"})]})]}),U=()=>(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(P,{}),(0,a.jsx)(N,{children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,a.jsx)(y,{}),(0,a.jsx)(q,{})]})}),(0,a.jsx)(L,{})]}),D=()=>{let[e,s]=(0,r.useState)("instructions");return(0,a.jsx)(b,{children:(0,a.jsx)("div",{children:(()=>{switch(e){case"instructions":default:return(0,a.jsx)(d,{onNext:()=>s("questions")});case"questions":return(0,a.jsx)(F,{onNext:()=>s("finishInterview")});case"finishInterview":return(0,a.jsx)(T,{onNext:()=>s("analysis")});case"analysis":return(0,a.jsx)(U,{})}})()})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[318,766,831,120,441,684,358],()=>s(6149)),_N_E=e.O()}]);