"use client";
import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";
import { interviewApi, ConversationMessage, Summary, VideoScores } from "@/lib/interview-api";

interface InterviewContextType {
  // Interview state
  currentQuestion: string;
  setCurrentQuestion: (question: string) => void;
  isInterviewStarted: boolean;
  setIsInterviewStarted: (started: boolean) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;

  // Interview data
  candidateName: string;
  setCandidateName: (name: string) => void;
  jobTitle: string;
  setJobTitle: (title: string) => void;
  experience: number;
  setExperience: (exp: number) => void;

  // Interview response data
  currentQuestionScore: number;
  totalScore: number;
  questionCount: number;
  questionScores: number[]; // Array to track individual question scores
  isInterviewCompleted: boolean;
  interviewSummary: Summary | null;

  // Video analysis
  videoScores: VideoScores | null;
  isAnalyzingVideo: boolean;
  analyzeVideo: () => Promise<void>;

  // Conversation history
  conversationHistory: ConversationMessage[];
  addToHistory: (message: ConversationMessage) => void;

  // API methods
  startInterview: () => Promise<void>;
  submitAnswer: (answer: string) => Promise<void>;

  // Error handling
  error: string | null;
  setError: (error: string | null) => void;
}

const InterviewContext = createContext<InterviewContextType | undefined>(undefined);

interface InterviewProviderProps {
  children: ReactNode;
}

export const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {
  // Interview state
  const [currentQuestion, setCurrentQuestion] = useState<string>("");
  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Interview data
  const [candidateName, setCandidateName] = useState<string>("Anas ALi");
  const [jobTitle, setJobTitle] = useState<string>("Software Engineer");
  const [experience, setExperience] = useState<number>(3);

  // Interview response data
  const [currentQuestionScore, setCurrentQuestionScore] = useState<number>(0);
  const [totalScore, setTotalScore] = useState<number>(0);
  const [questionCount, setQuestionCount] = useState<number>(0);
  const [questionScores, setQuestionScores] = useState<number[]>([]);
  const [isInterviewCompleted, setIsInterviewCompleted] = useState<boolean>(false);
  const [interviewSummary, setInterviewSummary] = useState<Summary | null>(null);

  // Conversation history
  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>([]);

  // Error handling
  const [error, setError] = useState<string | null>(null);

  // Video analysis state
  const [videoScores, setVideoScores] = useState<VideoScores | null>(null);
  const [isAnalyzingVideo, setIsAnalyzingVideo] = useState<boolean>(false);

  // Helper function to add messages to conversation history
  const addToHistory = useCallback((message: ConversationMessage) => {
    setConversationHistory(prev => [...prev, message]);
  }, []);

  // Start the interview by getting the first question
  const startInterview = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await interviewApi.startInterview(jobTitle, candidateName, experience);
      setCurrentQuestion(response.nextQuestion);
      setCurrentQuestionScore(response.currentQuestionScore);
      setIsInterviewCompleted(response.isInterviewCompleted);

      if (response.Summary) {
        setInterviewSummary(response.Summary);
      }

      // Add interviewer's first question to history
      addToHistory({
        role: 'interviewer',
        content: response.nextQuestion
      });

      setIsInterviewStarted(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to start interview";
      setError(errorMessage);
      console.error("Failed to start interview:", err);
    } finally {
      setIsLoading(false);
    }
  }, [jobTitle, candidateName, experience, addToHistory]);

  // Submit candidate's answer and get next question
  const submitAnswer = useCallback(async (answer: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Add candidate's answer to history
      const candidateMessage: ConversationMessage = {
        role: 'candidate',
        content: answer
      };

      const updatedHistory = [...conversationHistory, candidateMessage];
      addToHistory(candidateMessage);

      // Get next question from API
      const response = await interviewApi.continueInterview(
        jobTitle,
        candidateName,
        experience,
        updatedHistory
      );

      setCurrentQuestion(response.nextQuestion);
      setCurrentQuestionScore(response.currentQuestionScore);
      setIsInterviewCompleted(response.isInterviewCompleted);

      // Add current question score to total score and track individual scores
      if (response.currentQuestionScore > 0) {
        setTotalScore(prev => prev + response.currentQuestionScore);
        setQuestionCount(prev => prev + 1);
        setQuestionScores(prev => [...prev, response.currentQuestionScore]);
      }

      if (response.Summary) {
        setInterviewSummary(response.Summary);
      }

      // Add interviewer's next question to history (only if not completed)
      if (!response.isInterviewCompleted && response.nextQuestion) {
        addToHistory({
          role: 'interviewer',
          content: response.nextQuestion
        });
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to submit answer";
      setError(errorMessage);
      console.error("Failed to submit answer:", err);
    } finally {
      setIsLoading(false);
    }
  }, [jobTitle, candidateName, experience, conversationHistory, addToHistory]);

  // Analyze video transcript using Gemini API
  const analyzeVideo = useCallback(async () => {
    if (conversationHistory.length === 0) {
      console.warn('No conversation history available for video analysis');
      return;
    }

    setIsAnalyzingVideo(true);
    setError(null);

    try {
      const scores = await interviewApi.analyzeVideoTranscript(conversationHistory);
      setVideoScores(scores);
      console.log('Video analysis completed:', scores);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to analyze video";
      setError(errorMessage);
      console.error("Failed to analyze video:", err);
    } finally {
      setIsAnalyzingVideo(false);
    }
  }, [conversationHistory]);

  const value: InterviewContextType = {
    // Interview state
    currentQuestion,
    setCurrentQuestion,
    isInterviewStarted,
    setIsInterviewStarted,
    isLoading,
    setIsLoading,

    // Interview data
    candidateName,
    setCandidateName,
    jobTitle,
    setJobTitle,
    experience,
    setExperience,

    // Interview response data
    currentQuestionScore,
    totalScore,
    questionCount,
    questionScores,
    isInterviewCompleted,
    interviewSummary,

    // Video analysis
    videoScores,
    isAnalyzingVideo,
    analyzeVideo,

    // Conversation history
    conversationHistory,
    addToHistory,

    // API methods
    startInterview,
    submitAnswer,

    // Error handling
    error,
    setError,
  };

  return (
    <InterviewContext.Provider value={value}>
      {children}
    </InterviewContext.Provider>
  );
};

export const useInterview = (): InterviewContextType => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error('useInterview must be used within an InterviewProvider');
  }
  return context;
};
