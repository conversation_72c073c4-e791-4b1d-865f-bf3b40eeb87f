(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{16:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,h:()=>a});let n="DYNAMIC_SERVER_USAGE";class i extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}},35:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,a=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),f=Symbol.iterator,g=Object.prototype.hasOwnProperty,m=Object.assign;function y(e,t,r,n,i,o){return{$$typeof:a,type:e,key:t,ref:void 0!==(r=o.ref)?r:null,props:o}}function b(e){return"object"==typeof e&&null!==e&&e.$$typeof===a}var w=/\/+/g;function v(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function _(){}function E(e,t,r){if(null==e)return e;var s=[],c=0;return!function e(t,r,s,c,l){var u,d,p,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var m=!1;if(null===t)m=!0;else switch(g){case"bigint":case"string":case"number":m=!0;break;case"object":switch(t.$$typeof){case a:case o:m=!0;break;case h:return e((m=t._init)(t._payload),r,s,c,l)}}if(m)return l=l(t),m=""===c?"."+v(t,0):c,i(l)?(s="",null!=m&&(s=m.replace(w,"$&/")+"/"),e(l,r,s,"",function(e){return e})):null!=l&&(b(l)&&(u=l,d=s+(null==l.key||t&&t.key===l.key?"":(""+l.key).replace(w,"$&/")+"/")+m,l=y(u.type,d,void 0,void 0,void 0,u.props)),r.push(l)),1;m=0;var E=""===c?".":c+":";if(i(t))for(var S=0;S<t.length;S++)g=E+v(c=t[S],S),m+=e(c,r,s,g,l);else if("function"==typeof(S=null===(p=t)||"object"!=typeof p?null:"function"==typeof(p=f&&p[f]||p["@@iterator"])?p:null))for(t=S.call(t),S=0;!(c=t.next()).done;)g=E+v(c=c.value,S++),m+=e(c,r,s,g,l);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(_,_):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,s,c,l);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return m}(e,s,"","",function(e){return t.call(r,e,c++)}),s}function S(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function k(){return new WeakMap}function x(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:E,forEach:function(e,t,r){E(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return E(e,function(){t++}),t},toArray:function(e){return E(e,function(e){return e})||[]},only:function(e){if(!b(e))throw Error(n(143));return e}},t.Fragment=s,t.Profiler=l,t.StrictMode=c,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(k);void 0===(t=n.get(e))&&(t=x(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var a=arguments[n];if("function"==typeof a||"object"==typeof a&&null!==a){var o=t.o;null===o&&(t.o=o=new WeakMap),void 0===(t=o.get(a))&&(t=x(),o.set(a,t))}else null===(o=t.p)&&(t.p=o=new Map),void 0===(t=o.get(a))&&(t=x(),o.set(a,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var s=e.apply(null,arguments);return(n=t).s=1,n.v=s}catch(e){throw(s=t).s=2,s.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=m({},e.props),a=e.key,o=void 0;if(null!=t)for(s in void 0!==t.ref&&(o=void 0),void 0!==t.key&&(a=""+t.key),t)g.call(t,s)&&"key"!==s&&"__self"!==s&&"__source"!==s&&("ref"!==s||void 0!==t.ref)&&(i[s]=t[s]);var s=arguments.length-2;if(1===s)i.children=r;else if(1<s){for(var c=Array(s),l=0;l<s;l++)c[l]=arguments[l+2];i.children=c}return y(e.type,a,void 0,void 0,o,i)},t.createElement=function(e,t,r){var n,i={},a=null;if(null!=t)for(n in void 0!==t.key&&(a=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var s=Array(o),c=0;c<o;c++)s[c]=arguments[c+2];i.children=s}if(e&&e.defaultProps)for(n in o=e.defaultProps)void 0===i[n]&&(i[n]=o[n]);return y(e,a,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=b,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:S}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},58:(e,t,r)=>{"use strict";r.d(t,{xl:()=>o});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let a="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function o(){return a?new a:new i}},115:(e,t,r)=>{"use strict";r.d(t,{XN:()=>i,FP:()=>n});let n=(0,r(58).xl)();function i(e){let t=n.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},125:(e,t,r)=>{"use strict";let n,i,a,o,s,c,l;r.r(t),r.d(t,{default:()=>cL});var u={};r.r(u),r.d(u,{q:()=>nN,l:()=>n$});var d={};async function p(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(d),r.d(d,{middleware:()=>cj});let h=null;async function f(){if("phase-production-build"===process.env.NEXT_PHASE)return;h||(h=p());let e=await h;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function g(...e){let t=await p();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let m=null;function y(){return m||(m=f()),m}function b(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),y();class w extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class v extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class _ extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let E="_N_T_",S={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function k(e){var t,r,n,i,a,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=i,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!a||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function x(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...k(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function A(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...S,GROUP:{builtinReact:[S.reactServerComponents,S.actionBrowser],serverOnly:[S.reactServerComponents,S.actionBrowser,S.instrument,S.middleware],neutralTarget:[S.apiNode,S.apiEdge],clientOnly:[S.serverSideRendering,S.appPagesBrowser],bundled:[S.reactServerComponents,S.actionBrowser,S.serverSideRendering,S.appPagesBrowser,S.shared,S.instrument,S.middleware],appPages:[S.reactServerComponents,S.serverSideRendering,S.appPagesBrowser,S.actionBrowser]}});let T=Symbol("response"),R=Symbol("passThrough"),C=Symbol("waitUntil");class P{constructor(e,t){this[R]=!1,this[C]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[T]||(this[T]=Promise.resolve(e))}passThroughOnException(){this[R]=!0}waitUntil(e){if("external"===this[C].kind)return(0,this[C].function)(e);this[C].promises.push(e)}}class O extends P{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function I(e){return e.replace(/\/$/,"")||"/"}function N(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function U(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=N(e);return""+t+r+n+i}function j(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=N(e);return""+r+t+n+i}function $(e,t){if("string"!=typeof e)return!1;let{pathname:r}=N(e);return r===t||r.startsWith(t+"/")}let D=new WeakMap;function M(e,t){let r;if(!t)return{pathname:e};let n=D.get(t);n||(n=t.map(e=>e.toLowerCase()),D.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let a=i[1].toLowerCase(),o=n.indexOf(a);return o<0?{pathname:e}:(r=t[o],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let L=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function H(e,t){return new URL(String(e).replace(L,"localhost"),t&&String(t).replace(L,"localhost"))}let W=Symbol("NextURLInternal");class K{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[W]={url:H(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let a=function(e,t){var r,n;let{basePath:i,i18n:a,trailingSlash:o}=null!=(r=t.nextConfig)?r:{},s={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):o};i&&$(s.pathname,i)&&(s.pathname=function(e,t){if(!$(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(s.pathname,i),s.basePath=i);let c=s.pathname;if(s.pathname.startsWith("/_next/data/")&&s.pathname.endsWith(".json")){let e=s.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");s.buildId=e[0],c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(s.pathname=c)}if(a){let e=t.i18nProvider?t.i18nProvider.analyze(s.pathname):M(s.pathname,a.locales);s.locale=e.detectedLocale,s.pathname=null!=(n=e.pathname)?n:s.pathname,!e.detectedLocale&&s.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):M(c,a.locales)).detectedLocale&&(s.locale=e.detectedLocale)}return s}(this[W].url.pathname,{nextConfig:this[W].options.nextConfig,parseData:!0,i18nProvider:this[W].options.i18nProvider}),o=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[W].url,this[W].options.headers);this[W].domainLocale=this[W].options.i18nProvider?this[W].options.i18nProvider.detectDomainLocale(o):function(e,t,r){if(e)for(let a of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=a.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===a.defaultLocale.toLowerCase()||(null==(i=a.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return a}}(null==(t=this[W].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,o);let s=(null==(r=this[W].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[W].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[W].url.pathname=a.pathname,this[W].defaultLocale=s,this[W].basePath=a.basePath??"",this[W].buildId=a.buildId,this[W].locale=a.locale??s,this[W].trailingSlash=a.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&($(i,"/api")||$(i,"/"+t.toLowerCase()))?e:U(e,"/"+t)}((e={basePath:this[W].basePath,buildId:this[W].buildId,defaultLocale:this[W].options.forceLocale?void 0:this[W].defaultLocale,locale:this[W].locale,pathname:this[W].url.pathname,trailingSlash:this[W].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=I(t)),e.buildId&&(t=j(U(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=U(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:j(t,"/"):I(t)}formatSearch(){return this[W].url.search}get buildId(){return this[W].buildId}set buildId(e){this[W].buildId=e}get locale(){return this[W].locale??""}set locale(e){var t,r;if(!this[W].locale||!(null==(r=this[W].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[W].locale=e}get defaultLocale(){return this[W].defaultLocale}get domainLocale(){return this[W].domainLocale}get searchParams(){return this[W].url.searchParams}get host(){return this[W].url.host}set host(e){this[W].url.host=e}get hostname(){return this[W].url.hostname}set hostname(e){this[W].url.hostname=e}get port(){return this[W].url.port}set port(e){this[W].url.port=e}get protocol(){return this[W].url.protocol}set protocol(e){this[W].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[W].url=H(e),this.analyze()}get origin(){return this[W].url.origin}get pathname(){return this[W].url.pathname}set pathname(e){this[W].url.pathname=e}get hash(){return this[W].url.hash}set hash(e){this[W].url.hash=e}get search(){return this[W].url.search}set search(e){this[W].url.search=e}get password(){return this[W].url.password}set password(e){this[W].url.password=e}get username(){return this[W].url.username}set username(e){this[W].url.username=e}get basePath(){return this[W].basePath}set basePath(e){this[W].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new K(String(this),this[W].options)}}var B=r(724);let q=Symbol("internal request");class J extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);A(r),e instanceof Request?super(e,t):super(r,t);let n=new K(r,{headers:x(this.headers),nextConfig:t.nextConfig});this[q]={cookies:new B.RequestCookies(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[q].cookies}get nextUrl(){return this[q].nextUrl}get page(){throw new v}get ua(){throw new _}get url(){return this[q].url}}class z{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let V=Symbol("internal response"),F=new Set([301,302,303,307,308]);function G(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class X extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new B.ResponseCookies(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let a=Reflect.apply(e[n],e,i),o=new Headers(r);return a instanceof B.ResponseCookies&&r.set("x-middleware-set-cookie",a.getAll().map(e=>(0,B.stringifyCookie)(e)).join(",")),G(t,o),a};default:return z.get(e,n,i)}}});this[V]={cookies:n,url:t.url?new K(t.url,{headers:x(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[V].cookies}static json(e,t){let r=Response.json(e,t);return new X(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!F.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",A(e)),new X(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",A(e)),G(t,r),new X(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),G(e,t),new X(null,{...e,headers:t})}}function Y(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let Q="Next-Router-Prefetch",Z=["RSC","Next-Router-State-Tree",Q,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],ee="_rsc";class et extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new et}}class er extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return z.get(t,r,n);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return z.get(t,a,n)},set(t,r,n,i){if("symbol"==typeof r)return z.set(t,r,n,i);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return z.set(t,o??r,n,i)},has(t,r){if("symbol"==typeof r)return z.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&z.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return z.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||z.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return et.callable;default:return z.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new er(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}var en=r(535),ei=r(115);class ea extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new ea}}class eo{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return ea.callable;default:return z.get(e,t,r)}}})}}let es=Symbol.for("next.mutated.cookies");class ec{static wrap(e,t){let r=new B.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,a=()=>{let e=en.J.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new B.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},o=new Proxy(r,{get(e,t,r){switch(t){case es:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),o}finally{a()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),o}finally{a()}};default:return z.get(e,t,r)}}});return o}}function el(e){return"action"===e.phase}function eu(e){if(!el((0,ei.XN)(e)))throw new ea}var ed=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(ed||{}),ep=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(ep||{}),eh=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(eh||{}),ef=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ef||{}),eg=function(e){return e.startServer="startServer.startServer",e}(eg||{}),em=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(em||{}),ey=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(ey||{}),eb=function(e){return e.executeRoute="Router.executeRoute",e}(eb||{}),ew=function(e){return e.runHandler="Node.runHandler",e}(ew||{}),ev=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(ev||{}),e_=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(e_||{}),eE=function(e){return e.execute="Middleware.execute",e}(eE||{});let eS=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ek=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function ex(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:eA,propagation:eT,trace:eR,SpanStatusCode:eC,SpanKind:eP,ROOT_CONTEXT:eO}=n=r(956);class eI extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eN=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eI})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eC.ERROR,message:null==t?void 0:t.message})),e.end()},eU=new Map,ej=n.createContextKey("next.rootSpanId"),e$=0,eD=()=>e$++,eM={set(e,t,r){e.push({key:t,value:r})}};class eL{getTracerInstance(){return eR.getTracer("next.js","0.0.1")}getContext(){return eA}getTracePropagationData(){let e=eA.active(),t=[];return eT.inject(e,t,eM),t}getActiveScopeSpan(){return eR.getSpan(null==eA?void 0:eA.active())}withPropagatedContext(e,t,r){let n=eA.active();if(eR.getSpanContext(n))return t();let i=eT.extract(n,e,r);return eA.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:a,options:o}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},s=o.spanName??r;if(!eS.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||o.hideSpan)return a();let c=this.getSpanContext((null==o?void 0:o.parentSpan)??this.getActiveScopeSpan()),l=!1;c?(null==(t=eR.getSpanContext(c))?void 0:t.isRemote)&&(l=!0):(c=(null==eA?void 0:eA.active())??eO,l=!0);let u=eD();return o.attributes={"next.span_name":s,"next.span_type":r,...o.attributes},eA.with(c.setValue(ej,u),()=>this.getTracerInstance().startActiveSpan(s,o,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{eU.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ek.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};l&&eU.set(u,new Map(Object.entries(o.attributes??{})));try{if(a.length>1)return a(e,t=>eN(e,t));let t=a(e);if(ex(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eN(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eN(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return eS.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let a=arguments.length-1,o=arguments[a];if("function"!=typeof o)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(eA.active(),o);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eR.setSpan(eA.active(),e):void 0}getRootSpanAttributes(){let e=eA.active().getValue(ej);return eU.get(e)}setRootSpanAttribute(e,t){let r=eA.active().getValue(ej),n=eU.get(r);n&&n.set(e,t)}}let eH=(()=>{let e=new eL;return()=>e})(),eW="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eW);class eK{constructor(e,t,r,n){var i;let a=e&&function(e,t){let r=er.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,o=null==(i=r.get(eW))?void 0:i.value;this._isEnabled=!!(!a&&o&&e&&o===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eW,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:eW,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eB(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of k(r))n.append("set-cookie",e);for(let e of new B.ResponseCookies(n).getAll())t.set(e)}}var eq=r(802),eJ=r.n(eq);class ez extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}class eV{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new eV(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let eF=Symbol.for("@next/cache-handlers-map"),eG=Symbol.for("@next/cache-handlers-set"),eX=globalThis;function eY(){if(eX[eF])return eX[eF].entries()}async function eQ(e,t){if(!e)return t();let r=eZ(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eZ(e));await e1(e,t)}}function eZ(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function e0(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eX[eG])return eX[eG].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function e1(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([e0(r,e.incrementalCache),...Object.values(n),...i])}let e2=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class e5{disable(){throw e2}getStore(){}run(){throw e2}exit(){throw e2}enterWith(){throw e2}static bind(e){return e}}let e3="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,e6=e3?new e3:new e5;class e8{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eJ()),this.callbackQueue.pause()}after(e){if(ex(e))this.waitUntil||e4(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||e4();let r=ei.FP.getStore();r&&this.workUnitStores.add(r);let n=e6.getStore(),i=n?n.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let a=(t=async()=>{try{await e6.run({rootTaskSpawnPhase:i},()=>e())}catch(e){this.reportTaskError("function",e)}},e3?e3.bind(t):e5.bind(t));this.callbackQueue.add(a)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=en.J.getStore();if(!e)throw Object.defineProperty(new ez("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return eQ(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new ez("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function e4(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function e9(e){let t,r={then:(n,i)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,i))};return r}class e7{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function te(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let tt=Symbol.for("@next/request-context"),tr=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function tn(e,t,r){let n=[],i=r&&r.size>0;for(let t of tr(e))t=`${E}${t}`,n.push(t);if(t.pathname&&!i){let e=`${E}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=eY();if(r)for(let[n,i]of r)"getExpiration"in i&&t.set(n,e9(async()=>i.getExpiration(...e)));return t}(n)}}class ti extends J{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let ta={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},to=(e,t)=>eH().withPropagatedContext(e.headers,t,ta),ts=!1;async function tc(e){var t;let n,i;if(!ts&&(ts=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(905);e(),to=t(to)}await y();let a=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let o=new K(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...o.searchParams.keys()]){let t=o.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(o.searchParams.delete(r),t))o.searchParams.append(r,e);o.searchParams.delete(e)}}let s=o.buildId;o.buildId="";let c=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),l=c.has("x-nextjs-data"),u="1"===c.get("RSC");l&&"/index"===o.pathname&&(o.pathname="/");let d=new Map;if(!a)for(let e of Z){let t=e.toLowerCase(),r=c.get(t);null!==r&&(d.set(t,r),c.delete(t))}let p=new ti({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(ee),t?r.toString():r})(o).toString(),init:{body:e.request.body,headers:c,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});l&&Object.defineProperty(p,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:te()})}));let h=e.request.waitUntil??(null==(t=function(){let e=globalThis[tt];return null==e?void 0:e.get()}())?void 0:t.waitUntil),f=new O({request:p,page:e.page,context:h?{waitUntil:h}:void 0});if((n=await to(p,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=f.waitUntil.bind(f),r=new e7;return eH().trace(eE.execute,{spanName:`middleware ${p.method} ${p.nextUrl.pathname}`,attributes:{"http.target":p.nextUrl.pathname,"http.method":p.method}},async()=>{try{var n,a,o,c,l,u;let d=te(),h=await tn("/",p.nextUrl,null),g=(l=p.nextUrl,u=e=>{i=e},function(e,t,r,n,i,a,o,s,c,l,u){function d(e){r&&r.setHeader("Set-Cookie",e)}let p={};return{type:"request",phase:e,implicitTags:a,url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return p.headers||(p.headers=function(e){let t=er.from(e);for(let e of Z)t.delete(e.toLowerCase());return er.seal(t)}(t.headers)),p.headers},get cookies(){if(!p.cookies){let e=new B.RequestCookies(er.from(t.headers));eB(t,e),p.cookies=eo.seal(e)}return p.cookies},set cookies(value){p.cookies=value},get mutableCookies(){if(!p.mutableCookies){let e=function(e,t){let r=new B.RequestCookies(er.from(e));return ec.wrap(r,t)}(t.headers,o||(r?d:void 0));eB(t,e),p.mutableCookies=e}return p.mutableCookies},get userspaceMutableCookies(){return p.userspaceMutableCookies||(p.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return eu("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return eu("cookies().set"),e.set(...r),t};default:return z.get(e,r,n)}}});return t}(this.mutableCookies)),p.userspaceMutableCookies},get draftMode(){return p.draftMode||(p.draftMode=new eK(c,t,this.cookies,this.mutableCookies)),p.draftMode},renderResumeDataCache:s??null,isHmrRefresh:l,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",p,void 0,l,{},h,u,void 0,d,!1,void 0)),m=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:a,previouslyRevalidatedTags:o}){var s;let c={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(s=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?s:"/"+s,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:a,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new e8({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:o,refreshTagsByCacheKind:function(){let e=new Map,t=eY();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,e9(async()=>n.refreshTags()));return e}()};return r.store=c,c}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(a=e.request.nextConfig)||null==(n=a.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(c=e.request.nextConfig)||null==(o=c.experimental)?void 0:o.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:p.headers.has(Q),buildId:s??"",previouslyRevalidatedTags:[]});return await en.J.run(m,()=>ei.FP.run(g,e.handler,p,f))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(p,f)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let g=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&g&&(u||!a)){let t=new K(g,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});a||t.host!==p.nextUrl.host||(t.buildId=s||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=Y(t.toString(),o.toString());!a&&l&&n.headers.set("x-nextjs-rewrite",r),u&&i&&(o.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),o.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let m=null==n?void 0:n.headers.get("Location");if(n&&m&&!a){let t=new K(m,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===o.host&&(t.buildId=s||t.buildId,n.headers.set("Location",t.toString())),l&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",Y(t.toString(),o.toString()).url))}let b=n||X.next(),w=b.headers.get("x-middleware-override-headers"),v=[];if(w){for(let[e,t]of d)b.headers.set(`x-middleware-request-${e}`,t),v.push(e);v.length>0&&b.headers.set("x-middleware-override-headers",w+","+v.join(","))}return{response:b,waitUntil:("internal"===f[C].kind?Promise.all(f[C].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:p.fetchMetrics}}var tl=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},tu=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function td(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class tp{constructor(e,t,r){if(ic.add(this),il.set(this,{}),iu.set(this,void 0),id.set(this,void 0),tl(this,id,r,"f"),tl(this,iu,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(tu(this,il,"f")[e]=r)}get value(){return Object.keys(tu(this,il,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>tu(this,il,"f")[e]).join("")}chunk(e,t){let r=tu(this,ic,"m",ih).call(this);for(let n of tu(this,ic,"m",ip).call(this,{name:tu(this,iu,"f").name,value:e,options:{...tu(this,iu,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(tu(this,ic,"m",ih).call(this))}}il=new WeakMap,iu=new WeakMap,id=new WeakMap,ic=new WeakSet,ip=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return tu(this,il,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),tu(this,il,"f")[t]=i}return tu(this,id,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},ih=function(){let e={};for(let t in tu(this,il,"f"))delete tu(this,il,"f")?.[t],e[t]={name:t,value:"",options:{...tu(this,iu,"f").options,maxAge:0}};return e};class th extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class tf extends th{}tf.kind="signIn";class tg extends th{}tg.type="AdapterError";class tm extends th{}tm.type="AccessDenied";class ty extends th{}ty.type="CallbackRouteError";class tb extends th{}tb.type="ErrorPageLoop";class tw extends th{}tw.type="EventError";class tv extends th{}tv.type="InvalidCallbackUrl";class t_ extends tf{constructor(){super(...arguments),this.code="credentials"}}t_.type="CredentialsSignin";class tE extends th{}tE.type="InvalidEndpoints";class tS extends th{}tS.type="InvalidCheck";class tk extends th{}tk.type="JWTSessionError";class tx extends th{}tx.type="MissingAdapter";class tA extends th{}tA.type="MissingAdapterMethods";class tT extends th{}tT.type="MissingAuthorize";class tR extends th{}tR.type="MissingSecret";class tC extends tf{}tC.type="OAuthAccountNotLinked";class tP extends tf{}tP.type="OAuthCallbackError";class tO extends th{}tO.type="OAuthProfileParseError";class tI extends th{}tI.type="SessionTokenError";class tN extends tf{}tN.type="OAuthSignInError";class tU extends tf{}tU.type="EmailSignInError";class tj extends th{}tj.type="SignOutError";class t$ extends th{}t$.type="UnknownAction";class tD extends th{}tD.type="UnsupportedStrategy";class tM extends th{}tM.type="InvalidProvider";class tL extends th{}tL.type="UntrustedHost";class tH extends th{}tH.type="Verification";class tW extends tf{}tW.type="MissingCSRF";let tK=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class tB extends th{}tB.type="DuplicateConditionalUI";class tq extends th{}tq.type="MissingWebAuthnAutocomplete";class tJ extends th{}tJ.type="WebAuthnVerificationError";class tz extends tf{}tz.type="AccountNotLinked";class tV extends th{}tV.type="ExperimentalFeatureNotEnabled";let tF=!1;function tG(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let tX=!1,tY=!1,tQ=!1,tZ=["createVerificationToken","useVerificationToken","getUserByEmail"],t0=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],t1=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"],t2=()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")},t5=async(e,t,r,n,i)=>{let{crypto:{subtle:a}}=t2();return new Uint8Array(await a.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:n},await a.importKey("raw",t,"HKDF",!1,["deriveBits"]),i<<3))};function t3(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function t6(e,t,r,n,i){return t5(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=t3(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),t3(r,"salt"),function(e){let t=t3(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}let t8=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},t4=new TextEncoder,t9=new TextDecoder;function t7(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function re(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function rt(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return re(r,t,0),re(r,e%0x100000000,4),r}function rr(e){let t=new Uint8Array(4);return re(t,e),t}function rn(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:t9.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=t9.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function ri(e){let t=e;return("string"==typeof t&&(t=t4.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class ra extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ro extends ra{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class rs extends ra{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class rc extends ra{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class rl extends ra{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class ru extends ra{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class rd extends ra{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class rp extends ra{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class rh extends ra{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class rf extends ra{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function rg(e){if(!rm(e))throw Error("CryptoKey instance expected")}function rm(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function ry(e){return e?.[Symbol.toStringTag]==="KeyObject"}let rb=e=>rm(e)||ry(e),rw=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function rv(e){return rw(e)&&"string"==typeof e.kty}function r_(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let rE=(e,...t)=>r_("Key must be ",e,...t);function rS(e,t,...r){return r_(`Key for the ${e} algorithm must be `,t,...r)}async function rk(e){if(ry(e))if("secret"!==e.type)return e.export({format:"jwk"});else e=e.export();if(e instanceof Uint8Array)return{kty:"oct",k:ri(e)};if(!rm(e))throw TypeError(rE(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:i,...a}=await crypto.subtle.exportKey("jwk",e);return a}async function rx(e){return rk(e)}let rA=(e,t)=>{if("string"!=typeof e||!e)throw new rh(`${t} missing or invalid`)};async function rT(e,t){let r,n;if(rv(e))r=e;else if(rb(e))r=await rx(e);else throw TypeError(rE(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":rA(r.crv,'"crv" (Curve) Parameter'),rA(r.x,'"x" (X Coordinate) Parameter'),rA(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":rA(r.crv,'"crv" (Subtype of Key Pair) Parameter'),rA(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":rA(r.e,'"e" (Exponent) Parameter'),rA(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":rA(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new rl('"kty" (Key Type) Parameter missing or unsupported')}let i=t4.encode(JSON.stringify(n));return ri(await t8(t,i))}let rR=Symbol();function rC(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new rl(`Unsupported JWE Algorithm: ${e}`)}}let rP=e=>crypto.getRandomValues(new Uint8Array(rC(e)>>3)),rO=(e,t)=>{if(t.length<<3!==rC(e))throw new rd("Invalid Initialization Vector length")},rI=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new rd(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function rN(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function rU(e,t){return e.name===t}function rj(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!rU(e.algorithm,"AES-GCM"))throw rN("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw rN(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!rU(e.algorithm,"AES-KW"))throw rN("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw rN(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw rN("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!rU(e.algorithm,"PBKDF2"))throw rN("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!rU(e.algorithm,"RSA-OAEP"))throw rN("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw rN(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,i=r;if(i&&!n.usages.includes(i))throw TypeError(`CryptoKey does not support this operation, its usages must include ${i}.`)}async function r$(e,t,r,n,i){if(!(r instanceof Uint8Array))throw TypeError(rE(r,"Uint8Array"));let a=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(a>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,a>>3),{hash:`SHA-${a<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),l=t7(i,n,c,rt(i.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,l)).slice(0,a>>3)),iv:n}}async function rD(e,t,r,n,i){let a;r instanceof Uint8Array?a=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(rj(r,e,"encrypt"),a=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},a,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let rM=async(e,t,r,n,i)=>{if(!rm(r)&&!(r instanceof Uint8Array))throw TypeError(rE(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?rO(e,n):n=rP(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&rI(r,parseInt(e.slice(-3),10)),r$(e,t,r,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&rI(r,parseInt(e.slice(1,4),10)),rD(e,t,r,n,i);default:throw new rl("Unsupported JWE Content Encryption Algorithm")}};function rL(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function rH(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(rj(e,t,r),e)}async function rW(e,t,r){let n=await rH(t,e,"wrapKey");rL(n,e);let i=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",i,n,"AES-KW"))}async function rK(e,t,r){let n=await rH(t,e,"unwrapKey");rL(n,e);let i=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",i))}function rB(e){return t7(rr(e.length),e)}async function rq(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(rr(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await t8("sha256",n),32*t)}return i.slice(0,t>>3)}async function rJ(e,t,r,n,i=new Uint8Array(0),a=new Uint8Array(0)){let o;rj(e,"ECDH"),rj(t,"ECDH","deriveBits");let s=t7(rB(t4.encode(r)),rB(i),rB(a),rr(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,rq(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function rz(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let rV=(e,t)=>t7(t4.encode(e),new Uint8Array([0]),t);async function rF(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new rd("PBES2 Salt Input must be 8 or more octets");let i=rV(t,e),a=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:i},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(rj(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,a))}async function rG(e,t,r,n=2048,i=crypto.getRandomValues(new Uint8Array(16))){let a=await rF(i,e,n,t);return{encryptedKey:await rW(e.slice(-6),a,r),p2c:n,p2s:ri(i)}}async function rX(e,t,r,n,i){let a=await rF(i,e,n,t);return rK(e.slice(-6),a,r)}let rY=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},rQ=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new rl(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function rZ(e,t,r){return rj(t,e,"encrypt"),rY(e,t),new Uint8Array(await crypto.subtle.encrypt(rQ(e),t,r))}async function r0(e,t,r){return rj(t,e,"decrypt"),rY(e,t),new Uint8Array(await crypto.subtle.decrypt(rQ(e),t,r))}let r1=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new rl('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new rl('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new rl('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new rl('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},r2=async(e,t,r,n=!1)=>{let a=(i||=new WeakMap).get(e);if(a?.[r])return a[r];let o=await r1({...t,alg:r});return n&&Object.freeze(e),a?a[r]=o:i.set(e,{[r]:o}),o},r5=(e,t)=>{let r,n=(i||=new WeakMap).get(e);if(n?.[t])return n[t];let a="public"===e.type,o=!!a;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,a?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[a?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,a?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[a?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,a?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:i.set(e,{[t]:r}),r},r3=async(e,t)=>{if(e instanceof Uint8Array||rm(e))return e;if(ry(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return r5(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return r2(e,r,t)}if(rv(e))return e.k?rn(e.k):r2(e,e,t,!0);throw Error("unreachable")};function r6(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new rl(`Unsupported JWE Algorithm: ${e}`)}}let r8=e=>crypto.getRandomValues(new Uint8Array(r6(e)>>3));async function r4(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),i=new Uint8Array(await crypto.subtle.sign(r,n,e)),a=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=i[s]^a[s];return 0===o}async function r9(e,t,r,n,i,a){let o,s;if(!(t instanceof Uint8Array))throw TypeError(rE(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),u=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=t7(a,n,r,rt(a.length<<3)),p=new Uint8Array((await crypto.subtle.sign("HMAC",u,d)).slice(0,c>>3));try{o=await r4(i,p)}catch{}if(!o)throw new ru;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch{}if(!s)throw new ru;return s}async function r7(e,t,r,n,i,a){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(rj(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},o,t7(r,i)))}catch{throw new ru}}let ne=async(e,t,r,n,i,a)=>{if(!rm(t)&&!(t instanceof Uint8Array))throw TypeError(rE(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new rd("JWE Initialization Vector missing");if(!i)throw new rd("JWE Authentication Tag missing");switch(rO(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&rI(t,parseInt(e.slice(-3),10)),r9(e,t,r,n,i,a);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&rI(t,parseInt(e.slice(1,4),10)),r7(e,t,r,n,i,a);default:throw new rl("Unsupported JWE Content Encryption Algorithm")}};async function nt(e,t,r,n){let i=e.slice(0,7),a=await rM(i,r,t,n,new Uint8Array(0));return{encryptedKey:a.ciphertext,iv:ri(a.iv),tag:ri(a.tag)}}async function nr(e,t,r,n,i){return ne(e.slice(0,7),t,r,n,i,new Uint8Array(0))}let nn=async(e,t,r,n,i={})=>{let a,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(rg(r),!rz(r))throw new rl("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:u}=i;c=i.epk?await r3(i.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:p,crv:h,kty:f}=await rx(c),g=await rJ(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?r6(t):parseInt(e.slice(-5,-2),10),l,u);if(o={epk:{x:d,crv:h,kty:f}},"EC"===f&&(o.epk.y=p),l&&(o.apu=ri(l)),u&&(o.apv=ri(u)),"ECDH-ES"===e){s=g;break}s=n||r8(t);let m=e.slice(-6);a=await rW(m,g,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||r8(t),rg(r),a=await rZ(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||r8(t);let{p2c:c,p2s:l}=i;({encryptedKey:a,...o}=await rG(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||r8(t),a=await rW(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||r8(t);let{iv:c}=i;({encryptedKey:a,...o}=await nt(e,r,s,c));break}default:throw new rl('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:a,parameters:o}},ni=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},na=(e,t,r,n,i)=>{let a;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!a.has(o))throw new rl(`Extension Header Parameter "${o}" is not recognized`);if(void 0===i[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(a.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},no=e=>e?.[Symbol.toStringTag],ns=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},nc=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(rv(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&ns(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!rb(t))throw TypeError(rS(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${no(t)} instances for symmetric algorithms must be of type "secret"`)}},nl=(e,t,r)=>{if(rv(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&ns(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&ns(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!rb(t))throw TypeError(rS(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${no(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${no(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${no(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${no(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${no(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},nu=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?nc(e,t,r):nl(e,t,r)};class nd{#e;#t;#r;#n;#i;#a;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#i=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,i,a,o;if(!this.#t&&!this.#n&&!this.#r)throw new rd("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!ni(this.#t,this.#n,this.#r))throw new rd("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(na(rd,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new rl('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new rd('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new rd('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#a&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);nu("dir"===c?l:c,e,"encrypt");{let i,a=await r3(e,c);({cek:n,encryptedKey:r,parameters:i}=await nn(c,l,a,this.#a,this.#s)),i&&(t&&rR in t?this.#n?this.#n={...this.#n,...i}:this.setUnprotectedHeader(i):this.#t?this.#t={...this.#t,...i}:this.setProtectedHeader(i))}a=this.#t?t4.encode(ri(JSON.stringify(this.#t))):t4.encode(""),this.#i?(o=ri(this.#i),i=t7(a,t4.encode("."),t4.encode(o))):i=a;let{ciphertext:u,tag:d,iv:p}=await rM(l,this.#e,n,this.#o,i),h={ciphertext:ri(u)};return p&&(h.iv=ri(p)),d&&(h.tag=ri(d)),r&&(h.encrypted_key=ri(r)),o&&(h.aad=o),this.#t&&(h.protected=t9.decode(a)),this.#r&&(h.unprotected=this.#r),this.#n&&(h.header=this.#n),h}}class np{#c;constructor(e){this.#c=new nd(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let nh=e=>Math.floor(e.getTime()/1e3),nf=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,ng=e=>{let t,r=nf.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function nm(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let ny=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,nb=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class nw{#l;constructor(e){if(!rw(e))throw TypeError("JWT Claims Set MUST be an object");this.#l=structuredClone(e)}data(){return t4.encode(JSON.stringify(this.#l))}get iss(){return this.#l.iss}set iss(e){this.#l.iss=e}get sub(){return this.#l.sub}set sub(e){this.#l.sub=e}get aud(){return this.#l.aud}set aud(e){this.#l.aud=e}set jti(e){this.#l.jti=e}set nbf(e){"number"==typeof e?this.#l.nbf=nm("setNotBefore",e):e instanceof Date?this.#l.nbf=nm("setNotBefore",nh(e)):this.#l.nbf=nh(new Date)+ng(e)}set exp(e){"number"==typeof e?this.#l.exp=nm("setExpirationTime",e):e instanceof Date?this.#l.exp=nm("setExpirationTime",nh(e)):this.#l.exp=nh(new Date)+ng(e)}set iat(e){void 0===e?this.#l.iat=nh(new Date):e instanceof Date?this.#l.iat=nm("setIssuedAt",nh(e)):"string"==typeof e?this.#l.iat=nm("setIssuedAt",nh(new Date)+ng(e)):this.#l.iat=nm("setIssuedAt",e)}}class nv{#a;#o;#s;#t;#u;#d;#p;#h;constructor(e={}){this.#h=new nw(e)}setIssuer(e){return this.#h.iss=e,this}setSubject(e){return this.#h.sub=e,this}setAudience(e){return this.#h.aud=e,this}setJti(e){return this.#h.jti=e,this}setNotBefore(e){return this.#h.nbf=e,this}setExpirationTime(e){return this.#h.exp=e,this}setIssuedAt(e){return this.#h.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#u=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#p=!0,this}async encrypt(e,t){let r=new np(this.#h.data());return this.#t&&(this.#u||this.#d||this.#p)&&(this.#t={...this.#t,iss:this.#u?this.#h.iss:void 0,sub:this.#d?this.#h.sub:void 0,aud:this.#p?this.#h.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#a&&r.setContentEncryptionKey(this.#a),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function n_(e,t,r){let n;if(!rw(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return rn(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new rl('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return r1({...e,alg:t,ext:n});default:throw new rl('Unsupported "kty" (Key Type) Parameter value')}}let nE=async(e,t,r,n,i)=>{switch(e){case"dir":if(void 0!==r)throw new rd("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new rd("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,a;if(!rw(n.epk))throw new rd('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(rg(t),!rz(t))throw new rl("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await n_(n.epk,e);if(rg(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new rd('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=rn(n.apu)}catch{throw new rd("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new rd('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=rn(n.apv)}catch{throw new rd("Failed to base64url decode the apv")}}let s=await rJ(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?r6(n.enc):parseInt(e.slice(-5,-2),10),i,a);if("ECDH-ES"===e)return s;if(void 0===r)throw new rd("JWE Encrypted Key missing");return rK(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new rd("JWE Encrypted Key missing");return rg(t),r0(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let a;if(void 0===r)throw new rd("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new rd('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=i?.maxPBES2Count||1e4;if(n.p2c>o)throw new rd('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new rd('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{a=rn(n.p2s)}catch{throw new rd("Failed to base64url decode the p2s")}return rX(e,t,r,n.p2c,a)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new rd("JWE Encrypted Key missing");return rK(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,a;if(void 0===r)throw new rd("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new rd('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new rd('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=rn(n.iv)}catch{throw new rd("Failed to base64url decode the iv")}try{a=rn(n.tag)}catch{throw new rd("Failed to base64url decode the tag")}return nr(e,t,r,i,a)}default:throw new rl('Invalid or unsupported "alg" (JWE Algorithm) header value')}},nS=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function nk(e,t,r){let n,i,a,o,s,c,l;if(!rw(e))throw new rd("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new rd("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new rd("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new rd("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new rd("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new rd("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new rd("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new rd("JWE AAD incorrect type");if(void 0!==e.header&&!rw(e.header))throw new rd("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!rw(e.unprotected))throw new rd("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=rn(e.protected);n=JSON.parse(t9.decode(t))}catch{throw new rd("JWE Protected Header is invalid")}if(!ni(n,e.header,e.unprotected))throw new rd("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(na(rd,new Map,r?.crit,n,u),void 0!==u.zip)throw new rl('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=u;if("string"!=typeof d||!d)throw new rd("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new rd("missing JWE Encryption Algorithm (enc) in JWE Header");let h=r&&nS("keyManagementAlgorithms",r.keyManagementAlgorithms),f=r&&nS("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(h&&!h.has(d)||!h&&d.startsWith("PBES2"))throw new rc('"alg" (Algorithm) Header Parameter value not allowed');if(f&&!f.has(p))throw new rc('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=rn(e.encrypted_key)}catch{throw new rd("Failed to base64url decode the encrypted_key")}let g=!1;"function"==typeof t&&(t=await t(n,e),g=!0),nu("dir"===d?p:d,t,"decrypt");let m=await r3(t,d);try{a=await nE(d,m,i,u,r)}catch(e){if(e instanceof TypeError||e instanceof rd||e instanceof rl)throw e;a=r8(p)}if(void 0!==e.iv)try{o=rn(e.iv)}catch{throw new rd("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=rn(e.tag)}catch{throw new rd("Failed to base64url decode the tag")}let y=t4.encode(e.protected??"");c=void 0!==e.aad?t7(y,t4.encode("."),t4.encode(e.aad)):y;try{l=rn(e.ciphertext)}catch{throw new rd("Failed to base64url decode the ciphertext")}let b={plaintext:await ne(p,a,l,o,s,c)};if(void 0!==e.protected&&(b.protectedHeader=n),void 0!==e.aad)try{b.additionalAuthenticatedData=rn(e.aad)}catch{throw new rd("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(b.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(b.unprotectedHeader=e.header),g)?{...b,key:m}:b}async function nx(e,t,r){if(e instanceof Uint8Array&&(e=t9.decode(e)),"string"!=typeof e)throw new rd("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:a,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new rd("Invalid Compact JWE");let l=await nk({ciphertext:o,iv:a||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}async function nA(e,t,r){let n=await nx(e,t,r),i=function(e,t,r={}){let n,i;try{n=JSON.parse(t9.decode(t))}catch{}if(!rw(n))throw new rp("JWT Claims Set must be a top-level JSON object");let{typ:a}=r;if(a&&("string"!=typeof e.typ||ny(e.typ)!==ny(a)))throw new ro('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:u}=r,d=[...o];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new ro(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new ro('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new ro('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!nb(n.aud,"string"==typeof l?[l]:l))throw new ro('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=ng(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,h=nh(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new ro('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new ro('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>h+i)throw new ro('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new ro('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=h-i)throw new rs('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=h-n.iat;if(e-i>("number"==typeof u?u:ng(u)))throw new rs('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new ro('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:a}=n;if(void 0!==a.iss&&a.iss!==i.iss)throw new ro('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==a.sub&&a.sub!==i.sub)throw new ro('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==a.aud&&JSON.stringify(a.aud)!==JSON.stringify(i.aud))throw new ro('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let o={payload:i,protectedHeader:a};return"function"==typeof t?{...o,key:n.key}:o}let nT=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,nR=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,nC=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,nP=/^[\u0020-\u003A\u003D-\u007E]*$/,nO=Object.prototype.toString,nI=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function nN(e,t){let r=new nI,n=e.length;if(n<2)return r;let i=t?.decode||nD,a=0;do{let t=e.indexOf("=",a);if(-1===t)break;let o=e.indexOf(";",a),s=-1===o?n:o;if(t>s){a=e.lastIndexOf(";",t-1)+1;continue}let c=nU(e,a,t),l=nj(e,t,c),u=e.slice(c,l);if(void 0===r[u]){let n=nU(e,t+1,s),a=nj(e,s,n),o=i(e.slice(n,a));r[u]=o}a=s+1}while(a<n);return r}function nU(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function nj(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function n$(e,t,r){let n=r?.encode||encodeURIComponent;if(!nT.test(e))throw TypeError(`argument name is invalid: ${e}`);let i=n(t);if(!nR.test(i))throw TypeError(`argument val is invalid: ${t}`);let a=e+"="+i;if(!r)return a;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);a+="; Max-Age="+r.maxAge}if(r.domain){if(!nC.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);a+="; Domain="+r.domain}if(r.path){if(!nP.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);a+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==nO.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);a+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.partitioned&&(a+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return a}function nD(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:nM}=u,nL=()=>Date.now()/1e3|0,nH="A256CBC-HS512";async function nW(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,a=Array.isArray(r)?r:[r],o=await nB(nH,a[0],i),s=await rT({kty:"oct",k:ri(o)},`sha${o.byteLength<<3}`);return await new nv(t).setProtectedHeader({alg:"dir",enc:nH,kid:s}).setIssuedAt().setExpirationTime(nL()+n).setJti(crypto.randomUUID()).encrypt(o)}async function nK(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:a}=await nA(t,async({kid:e,enc:t})=>{for(let r of i){let i=await nB(t,r,n);if(void 0===e||e===await rT({kty:"oct",k:ri(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[nH,"A256GCM"]});return a}async function nB(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await t6("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function nq({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:i}=e,a=n.origin;return t?a=await i.redirect({url:t,baseUrl:n.origin}):r&&(a=await i.redirect({url:r,baseUrl:n.origin})),{callbackUrl:a,callbackUrlCookie:a!==r?a:void 0}}let nJ="\x1b[31m",nz="\x1b[0m",nV={error(e){let t=e instanceof th?e.type:e.name;if(console.error(`${nJ}[auth][error]${nz} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${nJ}[auth][cause]${nz}:`,t.stack),r&&console.error(`${nJ}[auth][details]${nz}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${nz}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${nz} ${e}`,JSON.stringify(t,null,2))}};function nF(e){let t={...nV};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let nG=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:nX,l:nY}=u;async function nQ(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function nZ(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new t$("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:i}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new t$(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new t$(`Cannot parse action at ${e}`);let[i,a]=n;if(!nG.includes(i)||a&&!["signin","callback","webauthn-options"].includes(i))throw new t$(`Cannot parse action at ${e}`);return{action:i,providerId:"undefined"==a?void 0:a}}(r.pathname,t.basePath);return{url:r,action:n,providerId:i,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await nQ(e):void 0,cookies:nX(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=nF(t);r.error(n),r.debug("request",e)}}function n0(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:i}=e,a=nY(r,n,i);t.has("Set-Cookie")?t.append("Set-Cookie",a):t.set("Set-Cookie",a)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function n1(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function n2(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function n5({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[i,a]=t.split("|");if(a===await n1(`${i}${e.secret}`))return{csrfTokenVerified:r&&i===n,csrfToken:i}}let i=n2(32),a=await n1(`${i}${e.secret}`);return{cookie:`${i}|${a}`,csrfToken:i}}function n3(e,t){if(!t)throw new tW(`CSRF token was missing during an action ${e}`)}function n6(e){return null!==e&&"object"==typeof e}function n8(e,...t){if(!t.length)return e;let r=t.shift();if(n6(e)&&n6(r))for(let t in r)n6(r[t])?(n6(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),n8(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return n8(e,...t)}let n4=Symbol("skip-csrf-check"),n9=Symbol("return-type-raw"),n7=Symbol("custom-fetch"),ie=Symbol("conform-internal"),it=e=>ii({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),ir=e=>ii({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function ii(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function ia(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let io={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function is({authOptions:e,providerId:t,action:r,url:n,cookies:i,callbackUrl:a,csrfToken:o,csrfDisabled:s,isPost:c}){var l,u;let d=nF(e),{providers:p,provider:h}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),i=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:i,...a}=t,o=i?.id??a.id,s=n8(a,i,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=i?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=ia(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=ia(e.token,e.issuer),n=ia(e.userinfo,e.issuer),i=e.checks??["pkce"];return e.redirectProxyUrl&&(i.includes("state")||i.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:i,userinfo:n,profile:e.profile??it,account:e.account??ir}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[n7]??(e[n7]=i?.[n7]),e}return s}),a=i.find(({id:e})=>e===t);if(t&&!a){let e=i.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:i,provider:a}}({url:n,providerId:t,config:e}),f=!1;if((h?.type==="oauth"||h?.type==="oidc")&&h.redirectProxyUrl)try{f=new URL(h.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${h.redirectProxyUrl}`)}let g={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:h,cookies:n8(td(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:p,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:nW,decode:nK,...e.jwt},events:(l=e.events??{},u=d,Object.keys(l).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new tw(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let i=e[n];return await i(...r)}catch(r){let e=new tg(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...io,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:f,experimental:{...e.experimental}},m=[];if(s)g.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await n5({options:g,cookieValue:i?.[g.cookies.csrfToken.name],isPost:c,bodyValue:o});g.csrfToken=e,g.csrfTokenVerified=r,t&&m.push({name:g.cookies.csrfToken.name,value:t,options:g.cookies.csrfToken.options})}let{callbackUrl:y,callbackUrlCookie:b}=await nq({options:g,cookieValue:i?.[g.cookies.callbackUrl.name],paramValue:a});return g.callbackUrl=y,b&&m.push({name:g.cookies.callbackUrl.name,value:b,options:g.cookies.callbackUrl.options}),{options:g,cookies:m}}var ic,il,iu,id,ip,ih,ig,im,iy,ib,iw,iv,i_,iE,iS,ik,ix={},iA=[],iT=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,iR=Array.isArray;function iC(e,t){for(var r in t)e[r]=t[r];return e}function iP(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function iO(e,t,r){var n,i,a,o={};for(a in t)"key"==a?n=t[a]:"ref"==a?i=t[a]:o[a]=t[a];if(arguments.length>2&&(o.children=arguments.length>3?ig.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===o[a]&&(o[a]=e.defaultProps[a]);return iI(e,o,n,i,null)}function iI(e,t,r,n,i){var a={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==i?++iy:i,__i:-1,__u:0};return null==i&&null!=im.vnode&&im.vnode(a),a}function iN(e){return e.children}function iU(e,t){this.props=e,this.context=t}function ij(e,t){if(null==t)return e.__?ij(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?ij(e):null}function i$(e){(!e.__d&&(e.__d=!0)&&ib.push(e)&&!iD.__r++||iw!==im.debounceRendering)&&((iw=im.debounceRendering)||iv)(iD)}function iD(){var e,t,r,n,i,a,o,s;for(ib.sort(i_);e=ib.shift();)e.__d&&(t=ib.length,n=void 0,a=(i=(r=e).__v).__e,o=[],s=[],r.__P&&((n=iC({},i)).__v=i.__v+1,im.vnode&&im.vnode(n),iK(r.__P,n,i,r.__n,r.__P.namespaceURI,32&i.__u?[a]:null,o,null==a?ij(i):a,!!(32&i.__u),s),n.__v=i.__v,n.__.__k[n.__i]=n,iB(o,n,s),n.__e!=a&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),ib.length>t&&ib.sort(i_));iD.__r=0}function iM(e,t,r,n,i,a,o,s,c,l,u){var d,p,h,f,g,m=n&&n.__k||iA,y=t.length;for(r.__d=c,function(e,t,r){var n,i,a,o,s,c=t.length,l=r.length,u=l,d=0;for(e.__k=[],n=0;n<c;n++)null!=(i=t[n])&&"boolean"!=typeof i&&"function"!=typeof i?(o=n+d,(i=e.__k[n]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?iI(null,i,null,null,null):iR(i)?iI(iN,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?iI(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,a=null,-1!==(s=i.__i=function(e,t,r,n){var i=e.key,a=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&i==c.key&&a===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return s;s++}}return -1}(i,r,o,u))&&(u--,(a=r[s])&&(a.__u|=131072)),null==a||null===a.__v?(-1==s&&d--,"function"!=typeof i.type&&(i.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,i.__u|=65536))):i=e.__k[n]=null;if(u)for(n=0;n<l;n++)null!=(a=r[n])&&0==(131072&a.__u)&&(a.__e==e.__d&&(e.__d=ij(a)),function e(t,r,n){var i,a;if(im.unmount&&im.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||iq(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){im.__e(e,r)}i.base=i.__P=null}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&e(i[a],r,n||"function"!=typeof t.type);n||iP(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(a,a))}(r,t,m),c=r.__d,d=0;d<y;d++)null!=(h=r.__k[d])&&(p=-1===h.__i?ix:m[h.__i]||ix,h.__i=d,iK(e,h,p,i,a,o,s,c,l,u),f=h.__e,h.ref&&p.ref!=h.ref&&(p.ref&&iq(p.ref,null,h),u.push(h.ref,h.__c||f,h)),null==g&&null!=f&&(g=f),65536&h.__u||p.__k===h.__k?c=function e(t,r,n){var i,a;if("function"==typeof t.type){for(i=t.__k,a=0;i&&a<i.length;a++)i[a]&&(i[a].__=t,r=e(i[a],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=ij(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(h,c,e):"function"==typeof h.type&&void 0!==h.__d?c=h.__d:f&&(c=f.nextSibling),h.__d=void 0,h.__u&=-196609);r.__d=c,r.__e=g}function iL(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||iT.test(t)?r:r+"px"}function iH(e,t,r,n,i){var a;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||iL(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||iL(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])a=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=r,r?n?r.u=n.u:(r.u=iE,e.addEventListener(t,a?ik:iS,a)):e.removeEventListener(t,a?ik:iS,a);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function iW(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=iE++;else if(t.t<r.u)return;return r(im.event?im.event(t):t)}}}function iK(e,t,r,n,i,a,o,s,c,l){var u,d,p,h,f,g,m,y,b,w,v,_,E,S,k,x,A=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),a=[s=t.__e=r.__e]),(u=im.__b)&&u(t);e:if("function"==typeof A)try{if(y=t.props,b="prototype"in A&&A.prototype.render,w=(u=A.contextType)&&n[u.__c],v=u?w?w.props.value:u.__:n,r.__c?m=(d=t.__c=r.__c).__=d.__E:(b?t.__c=d=new A(y,v):(t.__c=d=new iU(y,v),d.constructor=A,d.render=iJ),w&&w.sub(d),d.props=y,d.state||(d.state={}),d.context=v,d.__n=n,p=d.__d=!0,d.__h=[],d._sb=[]),b&&null==d.__s&&(d.__s=d.state),b&&null!=A.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=iC({},d.__s)),iC(d.__s,A.getDerivedStateFromProps(y,d.__s))),h=d.props,f=d.state,d.__v=t,p)b&&null==A.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),b&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(b&&null==A.getDerivedStateFromProps&&y!==h&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(y,v),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(y,d.__s,v)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=y,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),_=0;_<d._sb.length;_++)d.__h.push(d._sb[_]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(y,d.__s,v),b&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(h,f,g)})}if(d.context=v,d.props=y,d.__P=e,d.__e=!1,E=im.__r,S=0,b){for(d.state=d.__s,d.__d=!1,E&&E(t),u=d.render(d.props,d.state,d.context),k=0;k<d._sb.length;k++)d.__h.push(d._sb[k]);d._sb=[]}else do d.__d=!1,E&&E(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++S<25);d.state=d.__s,null!=d.getChildContext&&(n=iC(iC({},n),d.getChildContext())),b&&!p&&null!=d.getSnapshotBeforeUpdate&&(g=d.getSnapshotBeforeUpdate(h,f)),iM(e,iR(x=null!=u&&u.type===iN&&null==u.key?u.props.children:u)?x:[x],t,r,n,i,a,o,s,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),m&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=a){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;a[a.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;im.__e(e,t,r)}else null==a&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,a,o,s,c){var l,u,d,p,h,f,g,m=r.props,y=t.props,b=t.type;if("svg"===b?i="http://www.w3.org/2000/svg":"math"===b?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=a){for(l=0;l<a.length;l++)if((h=a[l])&&"setAttribute"in h==!!b&&(b?h.localName===b:3===h.nodeType)){e=h,a[l]=null;break}}if(null==e){if(null===b)return document.createTextNode(y);e=document.createElementNS(i,b,y.is&&y),s&&(im.__m&&im.__m(t,a),s=!1),a=null}if(null===b)m===y||s&&e.data===y||(e.data=y);else{if(a=a&&ig.call(e.childNodes),m=r.props||ix,!s&&null!=a)for(m={},l=0;l<e.attributes.length;l++)m[(h=e.attributes[l]).name]=h.value;for(l in m)if(h=m[l],"children"==l);else if("dangerouslySetInnerHTML"==l)d=h;else if(!(l in y)){if("value"==l&&"defaultValue"in y||"checked"==l&&"defaultChecked"in y)continue;iH(e,l,null,h,i)}for(l in y)h=y[l],"children"==l?p=h:"dangerouslySetInnerHTML"==l?u=h:"value"==l?f=h:"checked"==l?g=h:s&&"function"!=typeof h||m[l]===h||iH(e,l,h,m[l],i);if(u)s||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),iM(e,iR(p)?p:[p],t,r,n,"foreignObject"===b?"http://www.w3.org/1999/xhtml":i,a,o,a?a[0]:r.__k&&ij(r,0),s,c),null!=a)for(l=a.length;l--;)iP(a[l]);s||(l="value","progress"===b&&null==f?e.removeAttribute("value"):void 0===f||f===e[l]&&("progress"!==b||f)&&("option"!==b||f===m[l])||iH(e,l,f,m[l],i),l="checked",void 0!==g&&g!==e[l]&&iH(e,l,g,m[l],i))}return e}(r.__e,t,r,n,i,a,o,c,l);(u=im.diffed)&&u(t)}function iB(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)iq(r[n],r[++n],r[++n]);im.__c&&im.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){im.__e(e,t.__v)}})}function iq(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){im.__e(e,r)}}function iJ(e,t,r){return this.constructor(e,r)}function iz(e,t){var r,n,i,a,o;r=e,im.__&&im.__(r,t),i=(n="function"==typeof iz)?null:iz&&iz.__k||t.__k,a=[],o=[],iK(t,r=(!n&&iz||t).__k=iO(iN,null,[r]),i||ix,ix,t.namespaceURI,!n&&iz?[iz]:i?null:t.firstChild?ig.call(t.childNodes):null,a,!n&&iz?iz:i?i.__e:t.firstChild,n,o),iB(a,r,o)}ig=iA.slice,im={__e:function(e,t,r,n){for(var i,a,o;t=t.__;)if((i=t.__c)&&!i.__)try{if((a=i.constructor)&&null!=a.getDerivedStateFromError&&(i.setState(a.getDerivedStateFromError(e)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),o=i.__d),o)return i.__E=i}catch(t){e=t}throw e}},iy=0,iU.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=iC({},this.state),"function"==typeof e&&(e=e(iC({},r),this.props)),e&&iC(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),i$(this))},iU.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),i$(this))},iU.prototype.render=iN,ib=[],iv="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,i_=function(e,t){return e.__v.__b-t.__v.__b},iD.__r=0,iE=0,iS=iW(!1),ik=iW(!0);var iV=/[\s\n\\/='"\0<>]/,iF=/^(xlink|xmlns|xml)([A-Z])/,iG=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,iX=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,iY=new Set(["draggable","spellcheck"]),iQ=/["&<]/;function iZ(e){if(0===e.length||!1===iQ.test(e))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var i0={},i1=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),i2=/[A-Z]/g;function i5(){this.__d=!0}var i3=null,i6,i8,i4,i9,i7={},ae=[],at=Array.isArray,ar=Object.assign;function an(e,t){var r,n=e.type,i=!0;return e.__c?(i=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=i7),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=ar({},r.state,n.getDerivedStateFromProps(r.props,r.state)):i&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!i&&r.componentWillUpdate&&r.componentWillUpdate(),i4&&i4(e),r.render(r.props,r.state,t)}var ai=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),aa=/["&<]/,ao=0;function as(e,t,r,n,i,a){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var l={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--ao,__i:-1,__u:0,__source:i,__self:a};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return im.vnode&&im.vnode(l),l}async function ac(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),a().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);return i.ok?i.json():void console.error("Failed to fetch options",i)}function i(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function a(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){a().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let al={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},au=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function ad({html:e,title:t,status:r,cookies:n,theme:i,headTags:a}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${au}</style><title>${t}</title>${a??""}</head><body class="__next-auth-theme-${i?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=im.__s;im.__s=!0,i6=im.__b,i8=im.diffed,i4=im.__r,i9=im.unmount;var i=iO(iN,null);i.__k=[e];try{var a=function e(t,r,n,i,a,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?iZ(t):t+"";if(at(t)){var l,u="";a.__k=t;for(var d=0;d<t.length;d++){var p=t[d];if(null!=p&&"boolean"!=typeof p){var h,f=e(p,r,n,i,a,o,s);"string"==typeof f?u+=f:(l||(l=[]),u&&l.push(u),u="",at(f)?(h=l).push.apply(h,f):l.push(f))}}return l?(u&&l.push(u),l):u}if(void 0!==t.constructor)return"";t.__=a,i6&&i6(t);var g=t.type,m=t.props;if("function"==typeof g){var y,b,w,v=r;if(g===iN){if("tpl"in m){for(var _="",E=0;E<m.tpl.length;E++)if(_+=m.tpl[E],m.exprs&&E<m.exprs.length){var S=m.exprs[E];if(null==S)continue;"object"==typeof S&&(void 0===S.constructor||at(S))?_+=e(S,r,n,i,t,o,s):_+=S}return _}if("UNSTABLE_comment"in m)return"\x3c!--"+iZ(m.UNSTABLE_comment)+"--\x3e";b=m.children}else{if(null!=(y=g.contextType)){var k=r[y.__c];v=k?k.props.value:y.__}var x=g.prototype&&"function"==typeof g.prototype.render;if(x)b=an(t,v),w=t.__c;else{t.__c=w={__v:t,context:v,props:t.props,setState:i5,forceUpdate:i5,__d:!0,__h:[]};for(var A=0;w.__d&&A++<25;)w.__d=!1,i4&&i4(t),b=g.call(w,m,v);w.__d=!0}if(null!=w.getChildContext&&(r=ar({},r,w.getChildContext())),x&&im.errorBoundaries&&(g.getDerivedStateFromError||w.componentDidCatch)){b=null!=b&&b.type===iN&&null==b.key&&null==b.props.tpl?b.props.children:b;try{return e(b,r,n,i,t,o,s)}catch(a){return g.getDerivedStateFromError&&(w.__s=g.getDerivedStateFromError(a)),w.componentDidCatch&&w.componentDidCatch(a,i7),w.__d?(b=an(t,r),null!=(w=t.__c).getChildContext&&(r=ar({},r,w.getChildContext())),e(b=null!=b&&b.type===iN&&null==b.key&&null==b.props.tpl?b.props.children:b,r,n,i,t,o,s)):""}finally{i8&&i8(t),t.__=null,i9&&i9(t)}}}b=null!=b&&b.type===iN&&null==b.key&&null==b.props.tpl?b.props.children:b;try{var T=e(b,r,n,i,t,o,s);return i8&&i8(t),t.__=null,im.unmount&&im.unmount(t),T}catch(a){if(!o&&s&&s.onError){var R=s.onError(a,t,function(a){return e(a,r,n,i,t,o,s)});if(void 0!==R)return R;var C=im.__e;return C&&C(a,t),""}if(!o||!a||"function"!=typeof a.then)throw a;return a.then(function a(){try{return e(b,r,n,i,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(b,r,n,i,t,o,s)},a)}})}}var P,O="<"+g,I="";for(var N in m){var U=m[N];if("function"!=typeof U||"class"===N||"className"===N){switch(N){case"children":P=U;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in m)continue;N="for";break;case"className":if("class"in m)continue;N="class";break;case"defaultChecked":N="checked";break;case"defaultSelected":N="selected";break;case"defaultValue":case"value":switch(N="value",g){case"textarea":P=U;continue;case"select":i=U;continue;case"option":i!=U||"selected"in m||(O+=" selected")}break;case"dangerouslySetInnerHTML":I=U&&U.__html;continue;case"style":"object"==typeof U&&(U=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var i="-"==r[0]?r:i0[r]||(i0[r]=r.replace(i2,"-$&").toLowerCase()),a=";";"number"!=typeof n||i.startsWith("--")||i1.has(i)||(a="px;"),t=t+i+":"+n+a}}return t||void 0}(U));break;case"acceptCharset":N="accept-charset";break;case"httpEquiv":N="http-equiv";break;default:if(iF.test(N))N=N.replace(iF,"$1:$2").toLowerCase();else{if(iV.test(N))continue;("-"===N[4]||iY.has(N))&&null!=U?U+="":n?iX.test(N)&&(N="panose1"===N?"panose-1":N.replace(/([A-Z])/g,"-$1").toLowerCase()):iG.test(N)&&(N=N.toLowerCase())}}null!=U&&!1!==U&&(O=!0===U||""===U?O+" "+N:O+" "+N+'="'+("string"==typeof U?iZ(U):U+"")+'"')}}if(iV.test(g))throw Error(g+" is not a valid HTML tag name in "+O+">");if(I||("string"==typeof P?I=iZ(P):null!=P&&!1!==P&&!0!==P&&(I=e(P,r,"svg"===g||"foreignObject"!==g&&n,i,t,o,s))),i8&&i8(t),t.__=null,i9&&i9(t),!I&&ai.has(g))return O+"/>";var j="</"+g+">",$=O+">";return at(I)?[$].concat(I,[j]):"string"!=typeof I?[$,I,j]:$+I+j}(e,i7,!1,void 0,i,!1,void 0);return at(a)?a.join(""):a}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{im.__c&&im.__c(e,ae),im.__s=n,ae.length=0}}(e)}</div></body></html>`}}function ap(e){let{url:t,theme:r,query:n,cookies:i,pages:a,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:i,callbackUrl:a})=>(e[t]={id:t,name:r,type:n,signinUrl:i,callbackUrl:a},e),{})}),signin(t,s){if(t)throw new t$("Unsupported action");if(a?.signIn){let t=`${a.signIn}${a.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:i}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return ad({cookies:i,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:i,email:a,error:o}=e;"undefined"!=typeof document&&i?.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor),"undefined"!=typeof document&&i?.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText);let s=o&&(al[o]??al.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return as("div",{className:"signin",children:[i?.brandColor&&as("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${i.brandColor}}`}}),i?.buttonText&&as("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),as("div",{className:"card",children:[s&&as("div",{className:"error",children:as("p",{children:s})}),i?.logo&&as("img",{src:i.logo,alt:"Logo",className:"logo"}),r.map((e,i)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return as("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?as("form",{action:e.signinUrl,method:"POST",children:[as("input",{type:"hidden",name:"csrfToken",value:t}),n&&as("input",{type:"hidden",name:"callbackUrl",value:n}),as("button",{type:"submit",className:"button",style:{"--provider-brand-color":l},tabIndex:0,children:[as("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&as("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i>0&&"email"!==r[i-1].type&&"credentials"!==r[i-1].type&&"webauthn"!==r[i-1].type&&as("hr",{}),"email"===e.type&&as("form",{action:e.signinUrl,method:"POST",children:[as("input",{type:"hidden",name:"csrfToken",value:t}),as("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),as("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:a,placeholder:"<EMAIL>",required:!0}),as("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&as("form",{action:e.callbackUrl,method:"POST",children:[as("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>as("div",{children:[as("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),as("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),as("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&as("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[as("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>as("div",{children:[as("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),as("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),as("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i+1<r.length&&as("hr",{})]},e.id)})]}),c&&as(iN,{children:as("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${ac})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>a?.signOut?{redirect:a.signOut,cookies:i}:ad({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return as("div",{className:"signout",children:[n?.brandColor&&as("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&as("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),as("div",{className:"card",children:[n?.logo&&as("img",{src:n.logo,alt:"Logo",className:"logo"}),as("h1",{children:"Signout"}),as("p",{children:"Are you sure you want to sign out?"}),as("form",{action:t?.toString(),method:"POST",children:[as("input",{type:"hidden",name:"csrfToken",value:r}),as("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>a?.verifyRequest?{redirect:`${a.verifyRequest}${t?.search??""}`,cookies:i}:ad({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return as("div",{className:"verify-request",children:[r.brandColor&&as("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),as("div",{className:"card",children:[r.logo&&as("img",{src:r.logo,alt:"Logo",className:"logo"}),as("h1",{children:"Check your email"}),as("p",{children:"A sign in link has been sent to your email address."}),as("p",{children:as("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>a?.error?{redirect:`${a.error}${a.error.includes("?")?"&":"?"}error=${e}`,cookies:i}:ad({cookies:i,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,i=`${t}/signin`,a={default:{status:200,heading:"Error",message:as("p",{children:as("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:as("div",{children:[as("p",{children:"There is a problem with the server configuration."}),as("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:as("div",{children:[as("p",{children:"You do not have permission to sign in."}),as("p",{children:as("a",{className:"button",href:i,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:as("div",{children:[as("p",{children:"The sign in link is no longer valid."}),as("p",{children:"It may have been used already or it may have expired."})]}),signin:as("a",{className:"button",href:i,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=a[r]??a.default;return{status:o,html:as("div",{className:"error",children:[n?.brandColor&&as("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),as("div",{className:"card",children:[n?.logo&&as("img",{src:n?.logo,alt:"Logo",className:"logo"}),as("h1",{children:s}),as("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function ah(e,t=Date.now()){return new Date(t+1e3*e)}async function af(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:i,jwt:a,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!i)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:p,getUserByAccount:h,getUserByEmail:f,linkAccount:g,createSession:m,getSessionAndUser:y,deleteSession:b}=i,w=null,v=null,_=!1,E="jwt"===s;if(e)if(E)try{let t=n.cookies.sessionToken.name;(w=await a.decode({...a,token:e,salt:t}))&&"sub"in w&&w.sub&&(v=await p(w.sub))}catch{}else{let t=await y(e);t&&(w=t.session,v=t.user)}if("email"===l.type){let r=await f(t.email);return r?(v?.id!==r.id&&!E&&e&&await b(e),v=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:v})):(v=await u({...t,emailVerified:new Date}),await o.createUser?.({user:v}),_=!0),{session:w=E?{}:await m({sessionToken:c(),userId:v.id,expires:ah(n.session.maxAge)}),user:v,isNewUser:_}}if("webauthn"===l.type){let e=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(v){if(e.id===v.id){let e={...l,userId:v.id};return{session:w,user:v,isNewUser:_,account:e}}throw new tz("The account is already associated with another user",{provider:l.provider})}w=E?{}:await m({sessionToken:c(),userId:e.id,expires:ah(n.session.maxAge)});let t={...l,userId:e.id};return{session:w,user:e,isNewUser:_,account:t}}{if(v){await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t});let e={...l,userId:v.id};return{session:w,user:v,isNewUser:_,account:e}}if(t.email?await f(t.email):null)throw new tz("Another account already exists with the same e-mail address",{provider:l.provider});v=await u({...t}),await o.createUser?.({user:v}),await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),w=E?{}:await m({sessionToken:c(),userId:v.id,expires:ah(n.session.maxAge)});let e={...l,userId:v.id};return{session:w,user:v,isNewUser:!0,account:e}}}let S=await h({providerAccountId:l.providerAccountId,provider:l.provider});if(S){if(v){if(S.id===v.id)return{session:w,user:v,isNewUser:_};throw new tC("The account is already associated with another user",{provider:l.provider})}return{session:w=E?{}:await m({sessionToken:c(),userId:S.id,expires:ah(n.session.maxAge)}),user:S,isNewUser:_}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:a,userId:s,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:a,provider:i,type:r,userId:s}),v)return await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),{session:w,user:v,isNewUser:_};let p=t.email?await f(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)v=p,_=!1;else throw new tC("Another account already exists with the same e-mail address",{provider:l.provider})}else v=await u({...t,emailVerified:null}),_=!0;return await o.createUser?.({user:v}),await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),{session:w=E?{}:await m({sessionToken:c(),userId:v.id,expires:ah(n.session.maxAge)}),user:v,isNewUser:_}}}function ag(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(a="oauth4webapi/v3.6.0");let am="ERR_INVALID_ARG_VALUE",ay="ERR_INVALID_ARG_TYPE";function ab(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let aw=Symbol(),av=Symbol(),a_=Symbol(),aE=Symbol(),aS=Symbol(),ak=Symbol(),ax=Symbol(),aA=new TextEncoder,aT=new TextDecoder;function aR(e){return"string"==typeof e?aA.encode(e):aT.decode(e)}function aC(e){return"string"==typeof e?s(e):o(e)}o=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},s=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw ab("The input to be decoded is not correctly encoded.",am,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw ab("The input to be decoded is not correctly encoded.",am,e)}};class aP extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oH,Error.captureStackTrace?.(this,this.constructor)}}class aO extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function aI(e,t,r){return new aO(e,{code:t,cause:r})}function aN(e,t){if(!(e instanceof CryptoKey))throw ab(`${t} must be a CryptoKey`,ay)}function aU(e,t){if(aN(e,t),"private"!==e.type)throw ab(`${t} must be a private CryptoKey`,am)}function aj(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function a$(e){ag(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e??{});if(a&&!t.has("user-agent")&&t.set("user-agent",a),t.has("authorization"))throw ab('"options.headers" must not include the "authorization" header name',am);return t}function aD(e,t){if(void 0!==t){if("function"==typeof t&&(t=t(e.href)),!(t instanceof AbortSignal))throw ab('"options.signal" must return or be an instance of AbortSignal',ay);return t}}function aM(e){return e.includes("//")?e.replace("//","/"):e}async function aL(e,t,r,n){if(!(e instanceof URL))throw ab(`"${t}" must be an instance of URL`,ay);a6(e,n?.[aw]!==!0);let i=r(new URL(e.href)),a=a$(n?.headers);return a.set("accept","application/json"),(n?.[aE]||fetch)(i.href,{body:void 0,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:aD(i,n?.signal)})}async function aH(e,t){return aL(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=aM(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":!function(e,t,r=!1){"/"===e.pathname?e.pathname=t:e.pathname=aM(`${t}/${r?e.pathname:e.pathname.replace(/(\/)$/,"")}`)}(e,".well-known/oauth-authorization-server");break;default:throw ab('"options.algorithm" must be "oidc" (default), or "oauth2"',am)}return e},t)}function aW(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw ab(`${r} must be a number`,ay,i);if(e>0)return;if(t){if(0!==e)throw ab(`${r} must be a non-negative number`,am,i);return}throw ab(`${r} must be a positive number`,am,i)}catch(e){if(n)throw aI(e.message,n,i);throw e}}function aK(e,t,r,n){try{if("string"!=typeof e)throw ab(`${t} must be a string`,ay,n);if(0===e.length)throw ab(`${t} must not be empty`,am,n)}catch(e){if(r)throw aI(e.message,r,n);throw e}}async function aB(e,t){if(!(e instanceof URL)&&e!==sh)throw ab('"expectedIssuerIdentifier" must be an instance of URL',ay);if(!ag(t,Response))throw ab('"response" must be an instance of Response',ay);if(200!==t.status)throw aI('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',oV,t);o5(t);let r=await sp(t);if(aK(r.issuer,'"response" body "issuer" property',oq,{body:r}),e!==sh&&new URL(r.issuer).href!==e.href)throw aI('"response" body "issuer" property does not match the expected value',oQ,{expected:e.href,body:r,attribute:"issuer"});return r}function aq(e){var t=e,r="application/json";if(og(t)!==r)throw aJ(t,r)}function aJ(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return aI(r,oz,e)}function az(){return aC(crypto.getRandomValues(new Uint8Array(32)))}async function aV(e){return aK(e,"codeVerifier"),aC(await crypto.subtle.digest("SHA-256",aR(e)))}function aF(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new aP("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new aP("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new aP("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new aP("unsupported CryptoKey algorithm name",{cause:e})}}function aG(e){let t=e?.[av];return"number"==typeof t&&Number.isFinite(t)?t:0}function aX(e){let t=e?.[a_];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function aY(){return Math.floor(Date.now()/1e3)}function aQ(e){if("object"!=typeof e||null===e)throw ab('"as" must be an object',ay);aK(e.issuer,'"as.issuer"')}function aZ(e){if("object"!=typeof e||null===e)throw ab('"client" must be an object',ay);aK(e.client_id,'"client.client_id"')}function a0(e,t){let r=aY()+aG(t);return{jti:az(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function a1(e,t,r){if(!r.usages.includes("sign"))throw ab('CryptoKey instances used for signing assertions must include "sign" in their "usages"',am);let n=`${aC(aR(JSON.stringify(e)))}.${aC(aR(JSON.stringify(t)))}`,i=aC(await crypto.subtle.sign(o9(r),r,aR(n)));return`${n}.${i}`}async function a2(e){let{kty:t,e:r,n,x:i,y:a,crv:o}=await crypto.subtle.exportKey("jwk",e),s={kty:t,e:r,n,x:i,y:a,crv:o};return c.set(e,s),s}async function a5(e){return(c||=new WeakMap).get(e)||a2(e)}let a3=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function a6(e,t){if(t&&"https:"!==e.protocol)throw aI("only requests to HTTPS are allowed",oF,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw aI("only HTTP and HTTPS requests are allowed",oG,e)}function a8(e,t,r,n){let i;if("string"!=typeof e||!(i=a3(e)))throw aI(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?o0:o1,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return a6(i,n),i}function a4(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?a8(e.mtls_endpoint_aliases[t],t,r,n):a8(e[t],t,r,n)}class a9 extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oL,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class a7 extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oW,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class oe extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oM,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let ot="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",or=RegExp("^[,\\s]*("+ot+")\\s(.*)"),on=RegExp("^[,\\s]*("+ot+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),oi=RegExp("^[,\\s]*"+("("+ot+")\\s*=\\s*(")+ot+")[,\\s]*(.*)"),oa=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function oo(e){if(e.status>399&&e.status<500){o5(e),aq(e);try{let t=await e.clone().json();if(aj(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function os(e,t,r){if(e.status!==t){let t;if(t=await oo(e))throw await e.body?.cancel(),new a9("server responded with an error in the response body",{cause:t,response:e});throw aI(`"response" is not a conform ${r} response (unexpected HTTP status code)`,oV,e)}}function oc(e){if(!oR.has(e))throw ab('"options.DPoP" is not a valid DPoPHandle',am)}async function ol(e,t,r,n,i,a){if(aK(e,'"accessToken"'),!(r instanceof URL))throw ab('"url" must be an instance of URL',ay);a6(r,a?.[aw]!==!0),n=a$(n),a?.DPoP&&(oc(a.DPoP),await a.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (a?.[aE]||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:aD(r,a?.signal)});return a?.DPoP?.cacheNonce(o),o}async function ou(e,t,r,n){aQ(e),aZ(t);let i=a4(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[aw]!==!0),a=a$(n?.headers);return t.userinfo_signed_response_alg?a.set("accept","application/jwt"):(a.set("accept","application/json"),a.append("accept","application/jwt")),ol(r,"GET",i,a,null,{...n,[av]:aG(t)})}function od(e,t,r,n){(l||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return aY()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function op(e,t){l?.delete(e),delete t?.jwks,delete t?.uat}async function oh(e,t,r){var n;let i,a,o,{alg:s,kid:c}=r;if(function(e){if(!o8(e.alg))throw new aP('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!l?.has(e)&&!("object"!=typeof(n=t?.[ax])||null===n||!("uat"in n)||"number"!=typeof n.uat||aY()-n.uat>=300)&&"jwks"in n&&aj(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,aj)&&od(e,t?.[ax].jwks,t?.[ax].uat),l?.has(e)){if({jwks:i,age:a}=l.get(e),a>=300)return op(e,t?.[ax]),oh(e,t,r)}else i=await o3(e,t).then(o6),a=0,od(e,i,aY(),t?.[ax]);switch(s.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new aP("unsupported JWS algorithm",{cause:{alg:s}})}let u=i.keys.filter(e=>{if(e.kty!==o||void 0!==c&&c!==e.kid||void 0!==e.alg&&s!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===s&&"P-256"!==e.crv:case"ES384"===s&&"P-384"!==e.crv:case"ES512"===s&&"P-521"!==e.crv:case"Ed25519"===s&&"Ed25519"!==e.crv:case"EdDSA"===s&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:p}=u;if(!p){if(a>=60)return op(e,t?.[ax]),oh(e,t,r);throw aI("error when selecting a JWT verification key, no applicable keys found",oZ,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)})}if(1!==p)throw aI('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',oZ,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)});return su(s,d)}let of=Symbol();function og(e){return e.headers.get("content-type")?.split(";")[0]}async function om(e,t,r,n,i){let a;if(aQ(e),aZ(t),!ag(n,Response))throw ab('"response" must be an instance of Response',ay);if(oS(n),200!==n.status)throw aI('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',oV,n);if(o5(n),"application/jwt"===og(n)){let{claims:r,jwt:o}=await se(await n.text(),sa.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),aG(t),aX(t),i?.[ak]).then(ok.bind(void 0,t.client_id)).then(oA.bind(void 0,e));ov.set(n,o),a=r}else{if(t.userinfo_signed_response_alg)throw aI("JWT UserInfo Response expected",oK,n);a=await sp(n)}if(aK(a.sub,'"response" body "sub" property',oq,{body:a}),r===of);else if(aK(r,'"expectedSubject"'),a.sub!==r)throw aI('unexpected "response" body "sub" property value',oQ,{expected:r,body:a,attribute:"sub"});return a}async function oy(e,t,r,n,i,a,o){return await r(e,t,i,a),a.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[aE]||fetch)(n.href,{body:i,headers:Object.fromEntries(a.entries()),method:"POST",redirect:"manual",signal:aD(n,o?.signal)})}async function ob(e,t,r,n,i,a){let o=a4(e,"token_endpoint",t.use_mtls_endpoint_aliases,a?.[aw]!==!0);i.set("grant_type",n);let s=a$(a?.headers);s.set("accept","application/json"),a?.DPoP!==void 0&&(oc(a.DPoP),await a.DPoP.addProof(o,s,"POST"));let c=await oy(e,t,r,o,i,s,a);return a?.DPoP?.cacheNonce(c),c}let ow=new WeakMap,ov=new WeakMap;function o_(e){if(!e.id_token)return;let t=ow.get(e);if(!t)throw ab('"ref" was already garbage collected or did not resolve from the proper sources',am);return t}async function oE(e,t,r,n,i){if(aQ(e),aZ(t),!ag(r,Response))throw ab('"response" must be an instance of Response',ay);oS(r),await os(r,200,"Token Endpoint"),o5(r);let a=await sp(r);if(aK(a.access_token,'"response" body "access_token" property',oq,{body:a}),aK(a.token_type,'"response" body "token_type" property',oq,{body:a}),a.token_type=a.token_type.toLowerCase(),"dpop"!==a.token_type&&"bearer"!==a.token_type)throw new aP("unsupported `token_type` value",{cause:{body:a}});if(void 0!==a.expires_in){let e="number"!=typeof a.expires_in?parseFloat(a.expires_in):a.expires_in;aW(e,!0,'"response" body "expires_in" property',oq,{body:a}),a.expires_in=e}if(void 0!==a.refresh_token&&aK(a.refresh_token,'"response" body "refresh_token" property',oq,{body:a}),void 0!==a.scope&&"string"!=typeof a.scope)throw aI('"response" body "scope" property must be a string',oq,{body:a});if(void 0!==a.id_token){aK(a.id_token,'"response" body "id_token" property',oq,{body:a});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(aW(t.default_max_age,!0,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await se(a.id_token,sa.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),aG(t),aX(t),i?.[ak]).then(oI.bind(void 0,o)).then(oT.bind(void 0,e)).then(ox.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw aI('ID Token "aud" (audience) claim includes additional untrusted audiences',oY,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw aI('unexpected ID Token "azp" (authorized party) claim value',oY,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&aW(s.auth_time,!1,'ID Token "auth_time" (authentication time)',oq,{claims:s}),ov.set(r,c),ow.set(a,s)}return a}function oS(e){let t;if(t=function(e){if(!ag(e,Response))throw ab('"response" must be an instance of Response',ay);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(or),i=t?.["1"].toLowerCase();if(n=t?.["2"],!i)return;let a={};for(;n;){let r,i;if(t=n.match(on)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse(`"${i}"`)}catch{}a[r.toLowerCase()]=i;continue}if(t=n.match(oi)){[,r,i,n]=t,a[r.toLowerCase()]=i;continue}if(t=n.match(oa)){if(Object.keys(a).length)break;[,e,n]=t;break}return}let o={scheme:i,parameters:a};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new oe("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function ok(e,t){return void 0!==t.claims.aud?ox(e,t):t}function ox(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw aI('unexpected JWT "aud" (audience) claim value',oY,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw aI('unexpected JWT "aud" (audience) claim value',oY,{expected:e,claims:t.claims,claim:"aud"});return t}function oA(e,t){return void 0!==t.claims.iss?oT(e,t):t}function oT(e,t){let r=e[sf]?.(t)??e.issuer;if(t.claims.iss!==r)throw aI('unexpected JWT "iss" (issuer) claim value',oY,{expected:r,claims:t.claims,claim:"iss"});return t}let oR=new WeakSet,oC=Symbol();async function oP(e,t,r,n,i,a,o){if(aQ(e),aZ(t),!oR.has(n))throw ab('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',am);aK(i,'"redirectUri"');let s=so(n,"code");if(!s)throw aI('no authorization code in "callbackParameters"',oq);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",i),c.set("code",s),a!==oC&&(aK(a,'"codeVerifier"'),c.set("code_verifier",a)),ob(e,t,r,"authorization_code",c,o)}let oO={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function oI(e,t){for(let r of e)if(void 0===t.claims[r])throw aI(`JWT "${r}" (${oO[r]}) claim missing`,oq,{claims:t.claims});return t}let oN=Symbol(),oU=Symbol();async function oj(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?o$(e,t,r,n.expectedNonce,n.maxAge,{[ak]:n[ak]}):oD(e,t,r,n)}async function o$(e,t,r,n,i,a){let o=[];switch(n){case void 0:n=oN;break;case oN:break;default:aK(n,'"expectedNonce" argument'),o.push("nonce")}switch(i??=t.default_max_age){case void 0:i=oU;break;case oU:break;default:aW(i,!0,'"maxAge" argument'),o.push("auth_time")}let s=await oE(e,t,r,o,a);aK(s.id_token,'"response" body "id_token" property',oq,{body:s});let c=o_(s);if(i!==oU){let e=aY()+aG(t),r=aX(t);if(c.auth_time+i<e-r)throw aI("too much time has elapsed since the last End-User authentication",oX,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===oN){if(void 0!==c.nonce)throw aI('unexpected ID Token "nonce" claim value',oY,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw aI('unexpected ID Token "nonce" claim value',oY,{expected:n,claims:c,claim:"nonce"});return s}async function oD(e,t,r,n){let i=await oE(e,t,r,void 0,n),a=o_(i);if(a){if(void 0!==t.default_max_age){aW(t.default_max_age,!0,'"client.default_max_age"');let e=aY()+aG(t),r=aX(t);if(a.auth_time+t.default_max_age<e-r)throw aI("too much time has elapsed since the last End-User authentication",oX,{claims:a,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==a.nonce)throw aI('unexpected ID Token "nonce" claim value',oY,{expected:void 0,claims:a,claim:"nonce"})}return i}let oM="OAUTH_WWW_AUTHENTICATE_CHALLENGE",oL="OAUTH_RESPONSE_BODY_ERROR",oH="OAUTH_UNSUPPORTED_OPERATION",oW="OAUTH_AUTHORIZATION_RESPONSE_ERROR",oK="OAUTH_JWT_USERINFO_EXPECTED",oB="OAUTH_PARSE_ERROR",oq="OAUTH_INVALID_RESPONSE",oJ="OAUTH_INVALID_REQUEST",oz="OAUTH_RESPONSE_IS_NOT_JSON",oV="OAUTH_RESPONSE_IS_NOT_CONFORM",oF="OAUTH_HTTP_REQUEST_FORBIDDEN",oG="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",oX="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",oY="OAUTH_JWT_CLAIM_COMPARISON_FAILED",oQ="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",oZ="OAUTH_KEY_SELECTION_FAILED",o0="OAUTH_MISSING_SERVER_METADATA",o1="OAUTH_INVALID_SERVER_METADATA";function o2(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw aI('unexpected JWT "typ" header parameter value',oq,{header:t.header});return t}function o5(e){if(e.bodyUsed)throw ab('"response" body has been used already',am)}async function o3(e,t){aQ(e);let r=a4(e,"jwks_uri",!1,t?.[aw]!==!0),n=a$(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[aE]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:aD(r,t?.signal)})}async function o6(e){if(!ag(e,Response))throw ab('"response" must be an instance of Response',ay);if(200!==e.status)throw aI('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',oV,e);o5(e);let t=await sp(e,e=>(function(e,...t){if(!t.includes(og(e)))throw aJ(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw aI('"response" body "keys" property must be an array',oq,{body:t});if(!Array.prototype.every.call(t.keys,aj))throw aI('"response" body "keys" property members must be JWK formatted objects',oq,{body:t});return t}function o8(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function o4(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new aP(`unsupported ${t.name} modulusLength`,{cause:e})}function o9(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new aP("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(o4(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new aP("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return o4(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new aP("unsupported CryptoKey algorithm name",{cause:e})}async function o7(e,t,r,n){let i=aR(`${e}.${t}`),a=o9(r);if(!await crypto.subtle.verify(a,r,n,i))throw aI("JWT signature verification failed",oq,{key:r,data:i,signature:n,algorithm:a})}async function se(e,t,r,n,i){let a,o,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==i)e=await i(e),{0:s,1:c,length:l}=e.split(".");else throw new aP("JWE decryption is not configured",{cause:e});if(3!==l)throw aI("Invalid JWT",oq,e);try{a=JSON.parse(aR(aC(s)))}catch(e){throw aI("failed to parse JWT Header body as base64url encoded JSON",oB,e)}if(!aj(a))throw aI("JWT Header must be a top level object",oq,e);if(t(a),void 0!==a.crit)throw new aP('no JWT "crit" header parameter extensions are supported',{cause:{header:a}});try{o=JSON.parse(aR(aC(c)))}catch(e){throw aI("failed to parse JWT Payload body as base64url encoded JSON",oB,e)}if(!aj(o))throw aI("JWT Payload must be a top level object",oq,e);let u=aY()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw aI('unexpected JWT "exp" (expiration time) claim type',oq,{claims:o});if(o.exp<=u-n)throw aI('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',oX,{claims:o,now:u,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw aI('unexpected JWT "iat" (issued at) claim type',oq,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw aI('unexpected JWT "iss" (issuer) claim type',oq,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw aI('unexpected JWT "nbf" (not before) claim type',oq,{claims:o});if(o.nbf>u+n)throw aI('unexpected JWT "nbf" (not before) claim value',oX,{claims:o,now:u,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw aI('unexpected JWT "aud" (audience) claim type',oq,{claims:o});return{header:a,claims:o,jwt:e}}async function st(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new aP(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let i=await crypto.subtle.digest(n,aR(e));return aC(i.slice(0,i.byteLength/2))}async function sr(e,t,r,n){return t===await st(e,r,n)}async function sn(e){if(e.bodyUsed)throw ab("form_post Request instances must contain a readable body",am,{cause:e});return e.text()}async function si(e){if("POST"!==e.method)throw ab("form_post responses are expected to use the POST method",am,{cause:e});if("application/x-www-form-urlencoded"!==og(e))throw ab("form_post responses are expected to use the application/x-www-form-urlencoded content-type",am,{cause:e});return sn(e)}function sa(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw aI('unexpected JWT "alg" header parameter',oq,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw aI('unexpected JWT "alg" header parameter',oq,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw aI('unexpected JWT "alg" header parameter',oq,{header:n,expected:r,reason:"default value"});return}throw aI('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function so(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw aI(`"${t}" parameter must be provided only once`,oq);return r}let ss=Symbol(),sc=Symbol();function sl(e,t,r,n){var i;if(aQ(e),aZ(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw ab('"parameters" must be an instance of URLSearchParams, or URL',ay);if(so(r,"response"))throw aI('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',oq,{parameters:r});let a=so(r,"iss"),o=so(r,"state");if(!a&&e.authorization_response_iss_parameter_supported)throw aI('response parameter "iss" (issuer) missing',oq,{parameters:r});if(a&&a!==e.issuer)throw aI('unexpected "iss" (issuer) response parameter value',oq,{expected:e.issuer,parameters:r});switch(n){case void 0:case sc:if(void 0!==o)throw aI('unexpected "state" response parameter encountered',oq,{expected:void 0,parameters:r});break;case ss:break;default:if(aK(n,'"expectedState" argument'),o!==n)throw aI(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',oq,{expected:n,parameters:r})}if(so(r,"error"))throw new a7("authorization response from the server is an error",{cause:r});let s=so(r,"id_token"),c=so(r,"token");if(void 0!==s||void 0!==c)throw new aP("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),oR.add(i),i}async function su(e,t){let{ext:r,key_ops:n,use:i,...a}=t;return crypto.subtle.importKey("jwk",a,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new aP("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function sd(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function sp(e,t=aq){let r;try{r=await e.json()}catch(r){throw t(e),aI('failed to parse "response" body as JSON',oB,r)}if(!aj(r))throw aI('"response" body must be a top level object',oq,{body:r});return r}let sh=Symbol(),sf=Symbol();async function sg(e,t,r){let{cookies:n,logger:i}=r,a=n[e],o=new Date;o.setTime(o.getTime()+9e5),i.debug(`CREATE_${e.toUpperCase()}`,{name:a.name,payload:t,COOKIE_TTL:900,expires:o});let s=await nW({...r.jwt,maxAge:900,token:{value:t},salt:a.name}),c={...a.options,expires:o};return{name:a.name,value:s,options:c}}async function sm(e,t,r){try{let{logger:n,cookies:i,jwt:a}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new tS(`${e} cookie was missing`);let o=await nK({...a,token:t,salt:i[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new tS(`${e} value could not be parsed`,{cause:t})}}function sy(e,t,r){let{logger:n,cookies:i}=t,a=i[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:a}),r.push({name:a.name,value:"",options:{...i[e].options,maxAge:0}})}function sb(e,t){return async function(r,n,i){let{provider:a,logger:o}=i;if(!a?.checks?.includes(e))return;let s=r?.[i.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await sm(t,s,i);return sy(t,i,n),c}}let sw={async create(e){let t=az(),r=await aV(t);return{cookie:await sg("pkceCodeVerifier",t,e),value:r}},use:sb("pkce","pkceCodeVerifier")},sv="encodedState",s_={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new tS("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:az()},i=await nW({secret:e.jwt.secret,token:n,salt:sv,maxAge:900});return{cookie:await sg("state",i,e),value:i}},use:sb("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await nK({secret:t.jwt.secret,token:e,salt:sv});if(r)return r;throw Error("Invalid state")}catch(e){throw new tS("State could not be decoded",{cause:e})}}},sE={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=az();return{cookie:await sg("nonce",t,e),value:t}},use:sb("nonce","nonce")},sS="encodedWebauthnChallenge",sk={create:async(e,t,r)=>({cookie:await sg("webauthnChallenge",await nW({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:sS,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],i=await sm("webauthnChallenge",n,e),a=await nK({secret:e.jwt.secret,token:i,salt:sS});if(sy("webauthnChallenge",e,r),!a)throw new tS("WebAuthn challenge was missing");return a}};function sx(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function sA(e,t,r){let n,i,a,{logger:o,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await aH(e,{[aw]:!0,[aE]:s[n7]});if(!(n=await aB(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":i=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=sx(e),n=sx(t),i=btoa(`${r}:${n}`);return`Basic ${i}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;aK(d=s.clientSecret,'"clientSecret"'),i=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":i=function(e,t){let r;aK(e,'"clientSecret"');let n=void 0;return async(t,i,a,o)=>{r||=await crypto.subtle.importKey("raw",aR(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=a0(t,i);n?.(s,c);let l=`${aC(aR(JSON.stringify(s)))}.${aC(aR(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,aR(l));a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",`${l}.${aC(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":i=function(e,t){var r;let{key:n,kid:i}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&aK(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return aU(n,'"clientPrivateKey.key"'),async(e,r,a,o)=>{let s={alg:aF(n),kid:i},c=a0(e,r);t?.[aS]?.(s,c),a.set("client_id",r.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",await a1(s,c,n))}}(s.token.clientPrivateKey,{[aS](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":i=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],h=await s_.use(t,p,r);try{a=sl(n,u,new URLSearchParams(e),s.checks.includes("state")?h:ss)}catch(e){if(e instanceof a7){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new tP("OAuth Provider returned an error",t)}throw e}let f=await sw.use(t,p,r),g=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(g=s.redirectProxyUrl);let m=await oP(n,u,i,a,g,f??"decoy",{[aw]:!0,[aE]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[n7]??fetch)(...e))});s.token?.conform&&(m=await s.token.conform(m.clone())??m);let y={},b="oidc"===s.type;if(s[ie])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await m.clone().json();if(e.error){let t={providerId:s.id,...e};throw new tP(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new rp("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new rp("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new rp("Invalid JWT");if(!n)throw new rp("JWTs must contain a payload");try{t=rn(n)}catch{throw new rp("Failed to base64url decode the payload")}try{r=JSON.parse(t9.decode(t))}catch{throw new rp("Failed to parse the decoded payload as JSON")}if(!rw(r))throw new rp("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),i=await aH(r,{[aE]:s[n7]});n=await aB(r,i)}}}let w=await oj(n,u,m,{expectedNonce:await sE.use(t,p,r),requireIdToken:b});if(b){let t=o_(w);if(y=t,s[ie]&&"apple"===s.id)try{y.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await ou(n,u,w.access_token,{[aE]:s[n7],[aw]:!0});y=await om(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:w,provider:s});e instanceof Object&&(y=e)}else if(l?.url){let e=await ou(n,u,w.access_token,{[aE]:s[n7],[aw]:!0});y=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await sT(y,s,w,o),profile:y,cookies:p}}async function sT(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new tO(r,{provider:t.id}))}}var sR=r(356).Buffer;async function sC(e,t,r,n){let i=await sU(e,t,r),{cookie:a}=await sk.create(e,i.challenge,r);return{status:200,cookies:[...n??[],a],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function sP(e,t,r,n){let i=await sN(e,t,r),{cookie:a}=await sk.create(e,i.challenge);return{status:200,cookies:[...n??[],a],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function sO(e,t,r){let n,{adapter:i,provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new th("Invalid WebAuthn Authentication response");let s=sD(s$(o.id)),c=await i.getAuthenticator(s);if(!c)throw new th(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await sk.use(e,t.cookies,r);try{var u;let r=a.getRelayingParty(e,t);n=await a.simpleWebAuthn.verifyAuthenticationResponse({...a.verifyAuthenticationOptions,expectedChallenge:l,response:o,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:sM(u.transports),credentialID:s$(u.credentialID),credentialPublicKey:s$(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new tJ(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new tJ("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new tg(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let h=await i.getAccount(c.providerAccountId,a.id);if(!h)throw new th(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let f=await i.getUser(h.userId);if(!f)throw new th(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:h.userId})}`);return{account:h,user:f}}async function sI(e,t,r){var n;let i,{provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new th("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await sk.use(e,t.cookies,r);if(!c)throw new th("Missing user registration data in WebAuthn challenge cookie");try{let r=a.getRelayingParty(e,t);i=await a.simpleWebAuthn.verifyRegistrationResponse({...a.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new tJ(e)}if(!i.verified||!i.registrationInfo)throw new tJ("WebAuthn registration response could not be verified");let l={providerAccountId:sD(i.registrationInfo.credentialID),provider:e.provider.id,type:a.type},u={providerAccountId:l.providerAccountId,counter:i.registrationInfo.counter,credentialID:sD(i.registrationInfo.credentialID),credentialPublicKey:sD(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function sN(e,t,r){let{provider:n,adapter:i}=e,a=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:a?.map(e=>({id:s$(e.credentialID),type:"public-key",transports:sM(e.transports)}))})}async function sU(e,t,r){let{provider:n,adapter:i}=e,a=r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n2(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:a?.map(e=>({id:s$(e.credentialID),type:"public-key",transports:sM(e.transports)}))})}function sj(e){let{provider:t,adapter:r}=e;if(!r)throw new tx("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new tM("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function s$(e){return new Uint8Array(sR.from(e,"base64"))}function sD(e){return sR.from(e).toString("base64")}function sM(e){return e?e.split(","):void 0}async function sL(e,t,r,n){if(!t.provider)throw new tM("Callback route called without provider");let{query:i,body:a,method:o,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:p,jwt:h,events:f,callbacks:g,session:{strategy:m,maxAge:y},logger:b}=t,w="jwt"===m;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?a:i;if(t.isOnRedirectProxy&&s?.state){let e=await s_.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return b.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let m=await sA(s,e.cookies,t);m.cookies.length&&n.push(...m.cookies),b.debug("authorization result",m);let{user:v,account:_,profile:E}=m;if(!v||!_||!E)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;o=await e({providerAccountId:_.providerAccountId,provider:c.id})}let S=await sH({user:o??v,account:_,profile:E},t);if(S)return{redirect:S,cookies:n};let{user:k,session:x,isNewUser:A}=await af(r.value,v,_,t);if(w){let e={name:k.name,email:k.email,picture:k.image,sub:k.id?.toString()},i=await g.jwt({token:e,user:k,account:_,profile:E,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*y);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:x.sessionToken,options:{...t.cookies.sessionToken.options,expires:x.expires}});if(await f.signIn?.({user:k,account:_,profile:E,isNewUser:A}),A&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=i?.token,a=i?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await l.useVerificationToken({identifier:a,token:await n1(`${e}${o}`)}),u=!!s,m=u&&s.expires.valueOf()<Date.now();if(!u||m||a&&s.identifier!==a)throw new tH({hasInvite:u,expired:m});let{identifier:b}=s,v=await l.getUserByEmail(b)??{id:crypto.randomUUID(),email:b,emailVerified:null},_={providerAccountId:v.email,userId:v.id,type:"email",provider:c.id},E=await sH({user:v,account:_},t);if(E)return{redirect:E,cookies:n};let{user:S,session:k,isNewUser:x}=await af(r.value,v,_,t);if(w){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},i=await g.jwt({token:e,user:S,account:_,isNewUser:x,trigger:x?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*y);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:k.sessionToken,options:{...t.cookies.sessionToken.options,expires:k.expires}});if(await f.signIn?.({user:S,account:_,isNewUser:x}),x&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=a??{};Object.entries(i??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:s,method:o,body:JSON.stringify(a)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new t_;let p={providerAccountId:l.id,type:"credentials",provider:c.id},m=await sH({user:l,account:p,credentials:e},t);if(m)return{redirect:m,cookies:n};let b={name:l.name,email:l.email,picture:l.image,sub:l.id},w=await g.jwt({token:b,user:l,account:p,isNewUser:!1,trigger:"signIn"});if(null===w)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:w,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*y);let o=r.chunk(i,{expires:a});n.push(...o)}return await f.signIn?.({user:l,account:p}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let i,a,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new th("Invalid action parameter");let c=sj(t);switch(s){case"authenticate":{let t=await sO(c,e,n);i=t.user,a=t.account;break}case"register":{let r=await sI(t,e,n);i=r.user,a=r.account,o=r.authenticator}}await sH({user:i,account:a},t);let{user:l,isNewUser:u,session:m,account:b}=await af(r.value,i,a,t);if(!b)throw new th("Error creating or finding account");if(o&&l.id&&await c.adapter.createAuthenticator({...o,userId:l.id}),w){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},i=await g.jwt({token:e,user:l,account:b,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await h.encode({...h,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*y);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:m.sessionToken,options:{...t.cookies.sessionToken.options,expires:m.expires}});if(await f.signIn?.({user:l,account:b,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new tM(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof th)throw t;let e=new ty(t,{provider:c.id});throw b.debug("callback route error details",{method:o,query:i,body:a}),e}}async function sH(e,t){let r,{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof th)throw e;throw new tm(e)}if(!r)throw new tm("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function sW(e,t,r,n,i){let{adapter:a,jwt:o,events:s,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},h=t.value;if(!h)return p;if("jwt"===u){try{let r=e.cookies.sessionToken.name,a=await o.decode({...o,token:h,salt:r});if(!a)throw Error("Invalid JWT");let l=await c.jwt({token:a,...n&&{trigger:"update"},session:i}),u=ah(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});p.body=n;let i=await o.encode({...o,token:l,salt:r}),a=t.chunk(i,{expires:u});p.cookies?.push(...a),await s.session?.({session:n,token:l})}else p.cookies?.push(...t.clean())}catch(e){l.error(new tk(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=a,u=await r(h);if(u&&u.session.expires.valueOf()<Date.now()&&(await o(h),u=null),u){let{user:t,session:r}=u,a=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*a,f=ah(d);o<=Date.now()&&await l({sessionToken:h,expires:f});let g=await c.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});p.body=g,p.cookies?.push({name:e.cookies.sessionToken.name,value:h,options:{...e.cookies.sessionToken.options,expires:f}}),await s.session?.({session:g})}else h&&p.cookies?.push(...t.clean())}catch(e){l.error(new tI(e))}return p}async function sK(e,t){let r,n,{logger:i,provider:a}=t,o=a.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(a.issuer),t=await aH(e,{[aE]:a[n7],[aw]:!0}),r=await aB(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=a.callbackUrl;!t.isOnRedirectProxy&&a.redirectProxyUrl&&(c=a.redirectProxyUrl,n=a.callbackUrl,i.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:a.clientId,redirect_uri:c,...a.authorization?.params},Object.fromEntries(a.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];a.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await s_.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),a.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===a.type&&(a.checks=["nonce"]);else{let{value:e,cookie:r}=await sw.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}let p=await sE.create(t);return p&&(s.set("nonce",p.value),u.push(p.cookie)),"oidc"!==a.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),i.debug("authorization url is ready",{url:o,cookies:u,provider:a}),{redirect:o.toString(),cookies:u}}async function sB(e,t){let r,{body:n}=e,{provider:i,callbacks:a,adapter:o}=t,s=(i.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await o.getUserByEmail(s)??c,u={providerAccountId:s,userId:l.id,type:"email",provider:i.id};try{r=await a.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new tm(e)}if(!r)throw new tm("AccessDenied");if("string"==typeof r)return{redirect:await a.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,h=await i.generateVerificationToken?.()??n2(32),f=new Date(Date.now()+(i.maxAge??86400)*1e3),g=i.secret??t.secret,m=new URL(t.basePath,t.url.origin),y=i.sendVerificationRequest({identifier:s,token:h,expires:f,url:`${m}/callback/${i.id}?${new URLSearchParams({callbackUrl:d,token:h,email:s})}`,provider:i,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),b=o.createVerificationToken?.({identifier:s,token:await n1(`${h}${g}`),expires:f});return await Promise.all([y,b]),{redirect:`${m}/verify-request?${new URLSearchParams({provider:i.id,type:i.type})}`}}async function sq(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await sK(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await sB(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function sJ(e,t,r){let{jwt:n,events:i,callbackUrl:a,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:a,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await i.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await i.signOut?.({session:e})}}catch(e){o.error(new tj(e))}return e.push(...t.clean()),{redirect:a,cookies:e}}async function sz(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,a=t.value;if(!a)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:a,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(a);if(e)return e.user}return null}async function sV(e,t,r,n){let i=sj(t),{provider:a}=i,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await sz(t,r),c=s?{user:s,exists:!0}:await a.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:i=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(i)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return sP(i,e,l,n);case"register":if("string"==typeof l?.email)return sC(i,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function sF(e,t){let{action:r,providerId:n,error:i,method:a}=e,o=t.skipCSRFCheck===n4,{options:s,cookies:c}=await is({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===a,csrfDisabled:o}),l=new tp(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===a){let t=ap({...s,query:e.query,cookies:c});switch(r){case"callback":return await sL(e,s,l,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(i);case"providers":return t.providers(s.providers);case"session":return await sW(s,l,c);case"signin":return t.signin(n,i);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await sV(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&n3(r,t),await sL(e,s,l,c);case"session":return n3(r,t),await sW(s,l,c,!0,e.body?.data);case"signin":return n3(r,t),await sq(e,c,s);case"signout":return n3(r,t),await sJ(c,l,s)}}throw new t$(`Cannot handle action: ${r}`)}function sG(e,t,r,n,i){let a,o=i?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)a=new URL(s),o&&"/"!==o&&"/"!==a.pathname&&(a.pathname!==o&&nF(i).warn("env-url-basepath-mismatch"),a.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",i=n.endsWith(":")?n:n+":";a=new URL(`${i}//${e}`)}let c=a.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function sX(e,t){let r=nF(t),n=await nZ(e,t);if(!n)return Response.json("Bad request.",{status:400});let i=function(e,t){let{url:r}=e,n=[];if(!tF&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new tL(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new tR("Please define a `secret`");let i=e.query?.callbackUrl;if(i&&!tG(i,r.origin))return new tv(`Invalid callback URL. Received: ${i}`);let{callbackUrl:a}=td(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??a.name];if(o&&!tG(o,r.origin))return new tv(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof i||i?.url||(e="userinfo"):e="token":e="authorization",e)return new tE(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)tX=!0;else if("email"===t.type)tY=!0;else if("webauthn"===t.type){var c;if(tQ=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new th(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new tB("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new tq(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(tX){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new tD("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new tT("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,d=[];if(tY||u?.strategy==="database"||!u?.strategy&&l)if(tY){if(!l)return new tx("Email login requires an adapter");d.push(...tZ)}else{if(!l)return new tx("Database session requires an adapter");d.push(...t0)}if(tQ){if(!t.experimental?.enableWebAuthn)return new tV("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new tx("WebAuthn requires an adapter");d.push(...t1)}if(l){let e=d.filter(e=>!(e in l));if(e.length)return new tA(`Required adapter methods were missing: ${e.join(", ")}`)}return tF||(tF=!0),n}(n,t);if(Array.isArray(i))i.forEach(r.warn);else if(i){if(r.error(i),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:a}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new tb(`The error page ${e?.error} should not require authentication`)),n0(ap({theme:a}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let a=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===n9;try{let e=await sF(n,t);if(o)return e;let r=n0(e),i=r.headers.get("Location");if(!a||!i)return r;return Response.json({url:i},{headers:r.headers})}catch(d){r.error(d);let i=d instanceof th;if(i&&o&&!a)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof th&&tK.has(d.type)?d.type:"Configuration"});d instanceof t_&&s.set("code",d.code);let c=i&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${s}`;if(a)return Response.json({url:u});return Response.redirect(u)}}r(280),"undefined"==typeof URLPattern||URLPattern;var sY=r(557),sQ=r(602),sZ=r(801);function s0(){let e=e6.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}function s1(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:i}=e.nextUrl;return new J(n.replace(i,r),e)}function s2(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||nF(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e[`AUTH_${n}_ID`],a=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:i,clientSecret:a,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=i),c.clientSecret??(c.clientSecret=a),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}new WeakMap;var s5=r(815);let s3={current:null},s6="function"==typeof s5.cache?s5.cache:e=>e,s8=console.warn;function s4(e){return function(...t){s8(e(...t))}}function s9(){let e="cookies",t=en.J.getStore(),r=ei.FP.getStore();if(t){if(r&&"after"===r.phase&&!s0())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return ce(eo.seal(new B.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new sQ.f(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var n=t.route,i=r;let e=s7.get(i);if(e)return e;let a=(0,sZ.W)(i.renderSignal,"`cookies()`");return s7.set(i,a),Object.defineProperties(a,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=cn(n,e);(0,sY.t3)(n,e,t,i)}},size:{get(){let e="`cookies().size`",t=cn(n,e);(0,sY.t3)(n,e,t,i)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${ct(arguments[0])})\``;let t=cn(n,e);(0,sY.t3)(n,e,t,i)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${ct(arguments[0])})\``;let t=cn(n,e);(0,sY.t3)(n,e,t,i)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${ct(arguments[0])})\``;let t=cn(n,e);(0,sY.t3)(n,e,t,i)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${ct(t)}, ...)\``:"`cookies().set(...)`"}let t=cn(n,e);(0,sY.t3)(n,e,t,i)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${ct(arguments[0])})\``:`\`cookies().delete(${ct(arguments[0])}, ...)\``;let t=cn(n,e);(0,sY.t3)(n,e,t,i)}},clear:{value:function(){let e="`cookies().clear()`",t=cn(n,e);(0,sY.t3)(n,e,t,i)}},toString:{value:function(){let e="`cookies().toString()`",t=cn(n,e);(0,sY.t3)(n,e,t,i)}}}),a}else"prerender-ppr"===r.type?(0,sY.Ui)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,sY.xI)(e,t,r);(0,sY.Pk)(t,r)}let a=(0,ei.XN)(e);return ce(el(a)?a.userspaceMutableCookies:a.cookies)}s6(e=>{try{s8(s3.current)}finally{s3.current=null}});let s7=new WeakMap;function ce(e){let t=s7.get(e);if(t)return t;let r=Promise.resolve(e);return s7.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):ci.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):ca.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function ct(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let cr=s4(cn);function cn(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function ci(){return this.getAll().map(e=>[e.name,e]).values()}function ca(e){for(let e of this.getAll())this.delete(e.name);return e}function co(){let e=en.J.getStore(),t=ei.FP.getStore();if(e){if(t&&"after"===t.phase&&!s0())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return cc(er.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new sQ.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,n=t;let i=cs.get(n);if(i)return i;let a=(0,sZ.W)(n.renderSignal,"`headers()`");return cs.set(n,a),Object.defineProperties(a,{append:{value:function(){let e=`\`headers().append(${cl(arguments[0])}, ...)\``,t=cd(r,e);(0,sY.t3)(r,e,t,n)}},delete:{value:function(){let e=`\`headers().delete(${cl(arguments[0])})\``,t=cd(r,e);(0,sY.t3)(r,e,t,n)}},get:{value:function(){let e=`\`headers().get(${cl(arguments[0])})\``,t=cd(r,e);(0,sY.t3)(r,e,t,n)}},has:{value:function(){let e=`\`headers().has(${cl(arguments[0])})\``,t=cd(r,e);(0,sY.t3)(r,e,t,n)}},set:{value:function(){let e=`\`headers().set(${cl(arguments[0])}, ...)\``,t=cd(r,e);(0,sY.t3)(r,e,t,n)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=cd(r,e);(0,sY.t3)(r,e,t,n)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=cd(r,e);(0,sY.t3)(r,e,t,n)}},keys:{value:function(){let e="`headers().keys()`",t=cd(r,e);(0,sY.t3)(r,e,t,n)}},values:{value:function(){let e="`headers().values()`",t=cd(r,e);(0,sY.t3)(r,e,t,n)}},entries:{value:function(){let e="`headers().entries()`",t=cd(r,e);(0,sY.t3)(r,e,t,n)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=cd(r,e);(0,sY.t3)(r,e,t,n)}}}),a}else"prerender-ppr"===t.type?(0,sY.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,sY.xI)("headers",e,t);(0,sY.Pk)(e,t)}return cc((0,ei.XN)("headers").headers)}let cs=new WeakMap;function cc(e){let t=cs.get(e);if(t)return t;let r=Promise.resolve(e);return cs.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function cl(e){return"string"==typeof e?`'${e}'`:"..."}let cu=s4(cd);function cd(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}function cp(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return ch(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return ch(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return cg(null);default:return t}}function ch(e,t){let r,n=cf.get(cp);return n||(r=cg(e),cf.set(e,r),r)}r(16);let cf=new WeakMap;function cg(e){let t=new cm(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class cm{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){cb("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){cb("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let cy=s4(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function cb(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}async function cw(e,t){return sX(new Request(sG("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function cv(e){return"function"==typeof e}function c_(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await co(),n=await e(void 0);return t?.(n),cw(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],i=r[1],a=await e(n);return t?.(a),cE([n,i],a)}if(cv(r[0])){let n=r[0];return async(...r)=>{let i=await e(r[0]);return t?.(i),cE(r,i,n)}}let n="req"in r[0]?r[0].req:r[0],i="res"in r[0]?r[0].res:r[1],a=await e(n);return t?.(a),cw(new Headers(n.headers),a).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve(co()).then(t=>cw(t,e).then(e=>e.json()));if(t[0]instanceof Request)return cE([t[0],t[1]],e);if(cv(t[0])){let r=t[0];return async(...t)=>cE(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return cw(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function cE(e,t,r){let n=s1(e[0]),i=await cw(n.headers,t),a=await i.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:a}));let s=X.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),i=Object.values(r.pages??{});return(cS.has(n)||i.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=a,s=await r(n,e[1])??X.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=X.redirect(t)}}let c=new Response(s?.body,s);for(let e of i.headers.getSetCookie())c.headers.append("set-cookie",e);return c}let cS=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var ck=r(821),cx=r(167);let cA=r(830).s;function cT(e,t){var r;throw null!=t||(t=(null==cA||null==(r=cA.getStore())?void 0:r.isAction)?cx.zB.push:cx.zB.replace),function(e,t,r){void 0===r&&(r=ck.Q.TemporaryRedirect);let n=Object.defineProperty(Error(cx.oJ),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n.digest=cx.oJ+";"+t+";"+e+";"+r+";",n}(e,t,ck.Q.TemporaryRedirect)}var cR=r(159);async function cC(e,t={},r,n){let i=new Headers(await co()),{redirect:a=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??i.get("Referer")??"/",l=sG("signin",i.get("x-forwarded-proto"),i,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),a&&cT(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,i=r?.id??n.id;if(i===e){d={id:i,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return a&&cT(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),i.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(u,{method:"POST",headers:i,body:new URLSearchParams({...s,callbackUrl:c})}),h=await sX(p,{...n,raw:n9,skipCSRFCheck:n4}),f=await s9();for(let e of h?.cookies??[])f.set(e.name,e.value,e.options);let g=(h instanceof Response?h.headers.get("Location"):h.redirect)??u;return a?cT(g):g}async function cP(e,t){let r=new Headers(await co());r.set("Content-Type","application/x-www-form-urlencoded");let n=sG("signout",r.get("x-forwarded-proto"),r,process.env,t),i=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),a=new Request(n,{method:"POST",headers:r,body:i}),o=await sX(a,{...t,raw:n9,skipCSRFCheck:n4}),s=await s9();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?cT(o.redirect):o}async function cO(e,t){let r=new Headers(await co());r.set("Content-Type","application/json");let n=new Request(sG("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),i=await sX(n,{...t,raw:n9,skipCSRFCheck:n4}),a=await s9();for(let e of i?.cookies??[])a.set(e.name,e.value,e.options);return i.body}cR.s8,cR.s8,cR.s8,r(792).X;let{handlers:cI,signIn:cN,signOut:cU,auth:cj}=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return s2(r),sX(s1(t),r)};return{handlers:{GET:t,POST:t},auth:c_(e,e=>s2(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return s2(i),cC(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return s2(r),cP(t,r)},unstable_update:async t=>{let r=await e(void 0);return s2(r),cO(t,r)}}}s2(e);let t=t=>sX(s1(t),e);return{handlers:{GET:t,POST:t},auth:c_(e),signIn:(t,r,n)=>cC(t,r,n,e),signOut:t=>cP(t,e),unstable_update:t=>cO(t,e)}}({providers:[function(e){let t=e?.enterprise?.baseUrl??"https://github.com",r=e?.enterprise?.baseUrl?`${e?.enterprise?.baseUrl}/api/v3`:"https://api.github.com";return{id:"github",name:"GitHub",type:"oauth",authorization:{url:`${t}/login/oauth/authorize`,params:{scope:"read:user user:email"}},token:`${t}/login/oauth/access_token`,userinfo:{url:`${r}/user`,async request({tokens:e,provider:t}){let n=await fetch(t.userinfo?.url,{headers:{Authorization:`Bearer ${e.access_token}`,"User-Agent":"authjs"}}).then(async e=>await e.json());if(!n.email){let t=await fetch(`${r}/user/emails`,{headers:{Authorization:`Bearer ${e.access_token}`,"User-Agent":"authjs"}});if(t.ok){let e=await t.json();n.email=(e.find(e=>e.primary)??e[0]).email}}return n}},profile:e=>({id:e.id.toString(),name:e.name??e.login,email:e.email,image:e.avatar_url}),style:{bg:"#24292f",text:"#fff"},options:e}},function(e){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:e}}],secret:process.env.AUTH_SECRET});r(199);let c$={...d},cD=c$.middleware||c$.default,cM="/middleware";if("function"!=typeof cD)throw Object.defineProperty(Error(`The Middleware "${cM}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function cL(e){return tc({...e,page:cM,handler:async(...e)=>{try{return await cD(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await g(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},159:(e,t,r)=>{"use strict";r.d(t,{RM:()=>a,s8:()=>i});let n=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401})),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}},167:(e,t,r)=>{"use strict";r.d(t,{nJ:()=>o,oJ:()=>i,zB:()=>a});var n=r(821);let i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof o&&!isNaN(s)&&s in n.Q}},199:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(159),i=r(167);function a(e){return(0,i.nJ)(e)||(0,n.RM)(e)}},201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return o},withRequest:function(){return a}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function a(e,t,r){let a=i(e,t);return a?n.run(a,r):r()}function o(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},280:(e,t,r)=>{var n;(()=>{var i={226:function(i,a){!function(o,s){"use strict";var c="function",l="undefined",u="object",d="string",p="major",h="model",f="name",g="type",m="vendor",y="version",b="architecture",w="console",v="mobile",_="tablet",E="smarttv",S="wearable",k="embedded",x="Amazon",A="Apple",T="ASUS",R="BlackBerry",C="Browser",P="Chrome",O="Firefox",I="Google",N="Huawei",U="Microsoft",j="Motorola",$="Opera",D="Samsung",M="Sharp",L="Sony",H="Xiaomi",W="Zebra",K="Facebook",B="Chromium OS",q="Mac OS",J=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},z=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},V=function(e,t){return typeof e===d&&-1!==F(t).indexOf(F(e))},F=function(e){return e.toLowerCase()},G=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===l?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,a,o,l,d=0;d<t.length&&!o;){var p=t[d],h=t[d+1];for(r=n=0;r<p.length&&!o&&p[r];)if(o=p[r++].exec(e))for(i=0;i<h.length;i++)l=o[++n],typeof(a=h[i])===u&&a.length>0?2===a.length?typeof a[1]==c?this[a[0]]=a[1].call(this,l):this[a[0]]=a[1]:3===a.length?typeof a[1]!==c||a[1].exec&&a[1].test?this[a[0]]=l?l.replace(a[1],a[2]):void 0:this[a[0]]=l?a[1].call(this,l,a[2]):void 0:4===a.length&&(this[a[0]]=l?a[3].call(this,l.replace(a[1],a[2])):s):this[a]=l||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(V(t[r][n],e))return"?"===r?s:r}else if(V(t[r],e))return"?"===r?s:r;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,y],[/opios[\/ ]+([\w\.]+)/i],[y,[f,$+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[f,$]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[f,"UC"+C]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+C],y],[/\bfocus\/([\w\.]+)/i],[y,[f,O+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[f,$+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[f,$+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[f,"MIUI "+C]],[/fxios\/([-\w\.]+)/i],[y,[f,O]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+C]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+C],y],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,K],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[f,P+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,P+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[f,"Android "+C]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[y,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[f,O+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,y],[/(cobalt)\/([\w\.]+)/i],[f,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,F]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",F]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,F]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[m,D],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[m,D],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[m,A],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[m,A],[g,_]],[/(macintosh);/i],[h,[m,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[m,M],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[m,N],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[m,N],[g,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[m,H],[g,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[m,H],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[m,"OPPO"],[g,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[m,"Vivo"],[g,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[h,[m,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[m,j],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[m,j],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[m,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[m,"LG"],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[m,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[m,"Nokia"],[g,v]],[/(pixel c)\b/i],[h,[m,I],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[m,I],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[m,L],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[m,L],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[m,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[m,x],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[m,x],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[h,m,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[m,R],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[m,T],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[m,T],[g,v]],[/(nexus 9)/i],[h,[m,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[h,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[m,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[m,"Meizu"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,h,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,h,[g,_]],[/(surface duo)/i],[h,[m,U],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[m,"Fairphone"],[g,v]],[/(u304aa)/i],[h,[m,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[h,[m,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[h,[m,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[h,[m,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[h,[m,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[m,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[h,[m,"NuVision"],[g,_]],[/\b(k88) b/i],[h,[m,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[h,[m,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[h,[m,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[h,[m,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[h,[m,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],h,[g,_]],[/\b(ns-?\w{0,9}) b/i],[h,[m,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[m,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],h,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],h,[g,v]],[/\b(ph-1) /i],[h,[m,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[m,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[h,[m,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[h,[m,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[h,[m,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[m,h,[g,v]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[m,U],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[m,W],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[m,W],[g,v]],[/smart-tv.+(samsung)/i],[m,[g,E]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[m,D],[g,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,E]],[/(apple) ?tv/i],[m,[h,A+" TV"],[g,E]],[/crkey/i],[[h,P+"cast"],[m,I],[g,E]],[/droid.+aft(\w)( bui|\))/i],[h,[m,x],[g,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[m,M],[g,E]],[/(bravia[\w ]+)( bui|\))/i],[h,[m,L],[g,E]],[/(mitv-\w{5}) bui/i],[h,[m,H],[g,E]],[/Hbbtv.*(technisat) (.*);/i],[m,h,[g,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,G],[h,G],[g,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,h,[g,w]],[/droid.+; (shield) bui/i],[h,[m,"Nvidia"],[g,w]],[/(playstation [345portablevi]+)/i],[h,[m,L],[g,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[m,U],[g,w]],[/((pebble))app/i],[m,h,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[m,A],[g,S]],[/droid.+; (glass) \d/i],[h,[m,I],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[h,[m,W],[g,S]],[/(quest( 2| pro)?)/i],[h,[m,K],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,k]],[/(aeobc)\b/i],[h,[m,x],[g,k]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[h,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[y,Y,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[y,Y,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,q],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,y],[/\(bb(10);/i],[y,[f,R]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[f,O+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[f,P+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,B],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,y],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,y]]},ee=function(e,t){if(typeof e===u&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==l&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:s,a=t?J(Z,t):Z,w=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=s,t[y]=s,X.call(t,n,a.browser),t[p]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,w&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[b]=s,X.call(e,n,a.cpu),e},this.getDevice=function(){var e={};return e[m]=s,e[h]=s,e[g]=s,X.call(e,n,a.device),w&&!e[g]&&i&&i.mobile&&(e[g]=v),w&&"Macintosh"==e[h]&&r&&typeof r.standalone!==l&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[h]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[f]=s,e[y]=s,X.call(e,n,a.engine),e},this.getOS=function(){var e={};return e[f]=s,e[y]=s,X.call(e,n,a.os),w&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,B).replace(/macos/i,q)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?G(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=z([f,y,p]),ee.CPU=z([b]),ee.DEVICE=z([h,m,g,w,v,E,_,S,k]),ee.ENGINE=ee.OS=z([f,y]),typeof a!==l?(i.exports&&(a=i.exports=ee),a.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof o!==l&&(o.UAParser=ee);var et=typeof o!==l&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete a[e]}return r.exports}o.ab="//",e.exports=o(226)})()},356:e=>{"use strict";e.exports=require("node:buffer")},521:e=>{"use strict";e.exports=require("node:async_hooks")},535:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=(0,r(58).xl)()},552:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return s},interceptFetch:function(){return c},reader:function(){return a}});let i=r(201),a={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function o(e,t){let{url:r,method:i,headers:a,body:o,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:h}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(a),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:o?n.from(await t.arrayBuffer()).toString("base64"):null,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:h}}}async function s(e,t){let r=(0,i.getTestReqInfo)(t,a);if(!r)return e(t);let{testData:s,proxyPort:c}=r,l=await o(s,t),u=await e(`http://localhost:${c}`,{method:"POST",body:JSON.stringify(l),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:p}=d;switch(p){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:h,headers:f,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:h,headers:new Headers(f)})}function c(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):s(e,new Request(t,r))},()=>{r.g.fetch=e}}},557:(e,t,r)=>{"use strict";r.d(t,{t3:()=>c,I3:()=>d,Ui:()=>l,xI:()=>o,Pk:()=>s});var n=r(815),i=r(16);r(602),r(115),r(535),r(801);let a="function"==typeof n.unstable_postpone;function o(e,t,r){let n=Object.defineProperty(new i.F(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function s(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function c(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=h(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n)}throw h(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function l(e,t,r){(function(){if(!a)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.unstable_postpone(u(e,t))}function u(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&p(e.message)}function p(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===p(u("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function h(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},602:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});class n extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}},724:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function c(e){if(!e)return;let[[t,r],...n]=s(e),{domain:i,expires:a,httponly:o,maxage:c,path:d,samesite:p,secure:h,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var m,y,b={name:t,value:decodeURIComponent(r),domain:i,...a&&{expires:new Date(a)},...o&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:d,...p&&{sameSite:l.includes(m=(m=p).toLowerCase())?m:void 0},...h&&{secure:!0},...g&&{priority:u.includes(y=(y=g).toLowerCase())?y:void 0},...f&&{partitioned:!0}};let e={};for(let t in b)b[t]&&(e[t]=b[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(a,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>s,parseSetCookie:()=>c,stringifyCookie:()=>o}),e.exports=((e,a,o,s)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let c of n(a))i.call(e,c)||c===o||t(e,c,{get:()=>a[c],enumerable:!(s=r(a,c))||s.enumerable});return e})(t({},"__esModule",{value:!0}),a);var l=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,a,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=i,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!a||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(i)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},792:(e,t,r)=>{"use strict";r.d(t,{X:()=>function e(t){if((0,a.p)(t)||"object"==typeof t&&null!==t&&"digest"in t&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t.digest||(0,s.h)(t)||(0,o.I3)(t)||"object"==typeof t&&null!==t&&t.$$typeof===i||(0,n.T)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}});var n=r(801);let i=Symbol.for("react.postpone");var a=r(199),o=r(557),s=r(16)},801:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===i}r.d(t,{T:()=>n,W:()=>s});let i="HANGING_PROMISE_REJECTION";class a extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=i}}let o=new WeakMap;function s(e,t){if(e.aborted)return Promise.reject(new a(t));{let r=new Promise((r,n)=>{let i=n.bind(null,new a(t)),s=o.get(e);if(s)s.push(i);else{let t=[i];o.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(c),r}}function c(){}},802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var s=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,i,a,o){var s=r?r+e:e;if(!this._events[s])return!1;var c,l,u=this._events[s],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,o),!0}for(l=1,c=Array(d-1);l<d;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var p,h=u.length;for(l=0;l<h;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),d){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,n);break;case 4:u[l].fn.call(u[l].context,t,n,i);break;default:if(!c)for(p=1,c=Array(d-1);p<d;p++)c[p-1]=arguments[p];u[l].fn.apply(u[l].context,c)}}return!0},s.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||i&&!s.once||n&&s.context!==n||o(this,a);else{for(var c=0,l=[],u=s.length;c<u;c++)(s[c].fn!==t||i&&!s[c].once||n&&s[c].context!==n)&&l.push(s[c]);l.length?this._events[a]=1===l.length?l[0]:l:o(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let a=i/2|0,o=n+a;0>=r(e[o],t)?(n=++o,i-=a+1):i=a}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let a=(e,t,r)=>new Promise((a,o)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void a(e);let s=setTimeout(()=>{if("function"==typeof r){try{a(r())}catch(e){o(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,s=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),o(s)},t);n(e.then(a,o),()=>{clearTimeout(s)})});e.exports=a,e.exports.default=a,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,n),o=!1}finally{o&&delete r[e]}return a.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),a=()=>{},o=new t.TimeoutError;class s extends e{constructor(e){var t,n,i,o;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=a,this._resolveIdle=a,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(o=null==(i=e.interval)?void 0:i.toString())?o:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=a,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=a,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let a=async()=>{this._pendingCount++,this._intervalCount++;try{let a=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(o)});n(await a)}catch(e){i(e)}this._next()};this._queue.enqueue(a,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=s})(),e.exports=i})()},815:(e,t,r)=>{"use strict";e.exports=r(35)},821:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});var n=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({})},830:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(0,r(58).xl)()},890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,s=0;s<a.length;s++){var c=a[s],l=c.indexOf("=");if(!(l<0)){var u=c.substr(0,l).trim(),d=c.substr(++l,c.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var c=e+"="+s;if(null!=a.maxAge){var l=a.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");c+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");c+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(c+="; HttpOnly"),a.secure&&(c+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return a},wrapRequestHandler:function(){return o}});let n=r(201),i=r(552);function a(){return(0,i.interceptFetch)(r.g.fetch)}function o(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},956:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),a=r(930),o="context",s=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(o,e,a.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(o,a.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),a=r(957),o=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,o.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:a.DiagLogLevel.INFO})=>{var n,s,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let l=(0,o.getGlobal)("diag"),u=(0,i.createLogLevelDiagLogger)(null!=(s=r.logLevel)?s:a.DiagLogLevel.INFO,e);if(l&&!r.suppressOverrideMessage){let e=null!=(c=Error().stack)?c:"<failed to generate stacktrace>";l.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),a=r(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(o,e,a.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(o,a.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),a=r(194),o=r(277),s=r(369),c=r(930),l="propagation",u=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(l,e,c.DiagAPI.instance())}inject(e,t,r=a.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=a.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(l,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(l)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),a=r(139),o=r(607),s=r(930),c="trace";class l{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=a.wrapSpanContext,this.isSpanContextValid=a.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,s.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=l},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function a(e){return e.getValue(i)||void 0}t.getBaggage=a,t.getActiveBaggage=function(){return a(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),a=r(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:a.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return a("debug",this._namespace,e)}error(...e){return a("error",this._namespace,e)}info(...e){return a("info",this._namespace,e)}warn(...e){return a("warn",this._namespace,e)}verbose(...e){return a("verbose",this._namespace,e)}}function a(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),a=r(130),o=i.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var a;let o=c[s]=null!=(a=c[s])?a:{version:i.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(o.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return o[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=c[s])?void 0:t.version;if(n&&(0,a.isCompatible)(n))return null==(r=c[s])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=c[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function a(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||a.major!==s.major)return o(e);if(0===a.major)return a.minor===s.minor&&a.patch<=s.patch?(t.add(e),!0):o(e);return a.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=a,t.isCompatible=a(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class a extends n{add(e,t){}}t.NoopUpDownCounterMetric=a;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class c extends s{}t.NoopObservableCounterMetric=c;class l extends s{}t.NoopObservableGaugeMetric=l;class u extends s{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new a,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new l,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),a=r(403),o=r(139),s=n.ContextAPI.getInstance();class c{startSpan(e,t,r=s.active()){var n;if(null==t?void 0:t.root)return new a.NonRecordingSpan;let c=r&&(0,i.getSpanContext)(r);return"object"==typeof(n=c)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(c)?new a.NonRecordingSpan(c):new a.NonRecordingSpan}startActiveSpan(e,t,r,n){let a,o,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(a=t,c=r):(a=t,o=r,c=n);let l=null!=o?o:s.active(),u=this.startSpan(e,a,l),d=(0,i.setSpan)(l,u);return s.with(d,c,void 0,u)}}t.NoopTracer=c},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class a{getTracer(e,t,r){var i;return null!=(i=this.getDelegateTracer(e,t,r))?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=a},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),a=r(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function c(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(a.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return c(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let a=r.slice(0,i),o=r.slice(i+1,t.length);(0,n.validateKey)(a)&&(0,n.validateValue)(o)&&e.set(a,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,a=RegExp(`^(?:${n}|${i})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return a.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),a=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return a.test(e)&&e!==n.INVALID_TRACEID}function c(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=c,t.isSpanContextValid=function(e){return s(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var a=n[e]={exports:{}},o=!0;try{t[e].call(a.exports,a,a.exports,i),o=!1}finally{o&&delete n[e]}return a.exports}i.ab="//";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var o=i(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=i(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var c=i(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var l=i(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return l.ProxyTracer}});var u=i(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=i(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var p=i(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return p.SpanKind}});var h=i(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return h.SpanStatusCode}});var f=i(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=i(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=i(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var y=i(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return y.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return y.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return y.INVALID_SPAN_CONTEXT}});let b=i(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return b.context}});let w=i(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return w.diag}});let v=i(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return v.metrics}});let _=i(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return _.propagation}});let E=i(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return E.trace}}),a.default={context:b.context,diag:w.diag,metrics:v.metrics,propagation:_.propagation,trace:E.trace}})(),e.exports=a})()}},e=>{var t=e(e.s=125);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map