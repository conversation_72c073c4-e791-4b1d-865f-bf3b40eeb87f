"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[421],{3582:(e,r,a)=>{a.d(r,{Ih:()=>o,Il:()=>s,mJ:()=>n,pi:()=>l,zL:()=>i});var t=a(8309);let s=t.Ik({email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")}),n=t.Ik({confirmPassword:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),name:t.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")}),o=t.Ik({email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."})}),i=t.Ik({newPassword:t.Yj().min(6,{message:"Password must be at least 6 characters long"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),confirmPassword:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Password do not match",path:["confirmPassword"]}),l=t.Ik({otp1:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp2:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp3:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp4:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed")})},3999:(e,r,a)=>{a.d(r,{cn:()=>n});var t=a(2596),s=a(9688);function n(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}},7168:(e,r,a)=>{a.d(r,{$:()=>l});var t=a(5155);a(2115);var s=a(4624),n=a(2085),o=a(3999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:a,size:n,asChild:l=!1,...c}=e,d=l?s.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:a,size:n,className:r})),...c})}},9540:(e,r,a)=>{a.d(r,{lV:()=>d,MJ:()=>f,zB:()=>m,eI:()=>h,lR:()=>p,C5:()=>b});var t=a(5155),s=a(2115),n=a(4624),o=a(2177),i=a(3999),l=a(7073);function c(e){let{className:r,...a}=e;return(0,t.jsx)(l.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...a})}let d=o.Op,u=s.createContext({}),m=e=>{let{...r}=e;return(0,t.jsx)(u.Provider,{value:{name:r.name},children:(0,t.jsx)(o.xI,{...r})})},g=()=>{let e=s.useContext(u),r=s.useContext(x),{getFieldState:a}=(0,o.xW)(),t=(0,o.lN)({name:e.name}),n=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},x=s.createContext({});function h(e){let{className:r,...a}=e,n=s.useId();return(0,t.jsx)(x.Provider,{value:{id:n},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-3 ",r),...a})})}function p(e){let{className:r,...a}=e,{error:s,formItemId:n}=g();return(0,t.jsx)(c,{"data-slot":"form-label","data-error":!!s,className:(0,i.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...a})}function f(e){let{...r}=e,{error:a,formItemId:s,formDescriptionId:o,formMessageId:i}=g();return(0,t.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(o," ").concat(i):"".concat(o),"aria-invalid":!!a,...r})}function b(e){var r;let{className:a,...s}=e,{error:n,formMessageId:o}=g(),l=n?String(null!=(r=null==n?void 0:n.message)?r:""):s.children;return l?(0,t.jsx)("p",{"data-slot":"form-message",id:o,className:(0,i.cn)("text-sm !text-red-600",a),...s,children:l}):null}},9852:(e,r,a)=>{a.d(r,{p:()=>n});var t=a(5155);a(2115);var s=a(3999);function n(e){let{className:r,type:a,...n}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},9858:(e,r,a)=>{a.d(r,{A:()=>p});var t=a(5155),s=a(2115),n=a(221),o=a(2177),i=a(7168),l=a(9540),c=a(9852),d=a(6874),u=a.n(d),m=a(5695),g=a(6671);let x={HOME:"/",SIGN_IN:"/sign-in",SIGN_UP:"/sign-up"};var h=a(6766);let p=e=>{let{schema:r,defaultValues:a,formType:d,heading:p,placeholderValues:f}=e,b=(0,m.useRouter)(),v=(0,o.mN)({resolver:(0,n.u)(r),defaultValues:a,mode:"onChange"}),[w,j]=(0,s.useState)(null),[N,y]=(0,s.useState)(!1),[P,I]=(0,s.useState)(!1),[S,k]=(0,s.useState)(!1),_=async e=>{k(!0);try{await new Promise(e=>setTimeout(e,1e3)),g.oR.success("SIGN_IN"===d?"Successfully signed in! Welcome back.":"Account created successfully! Welcome to AI Interview."),setTimeout(()=>{b.push(x.HOME)},1e3)}catch(e){console.error("Authentication error:",e),g.oR.error("SIGN_IN"===d?"Sign in failed. Please check your credentials.":"Account creation failed. Please try again.")}finally{k(!1)}},A="Continue";return"SIGN_IN"===d?A="Sign In":"SIGN_UP"===d?A="Create an Account":"RESET_PASSWORD"===d&&(A="Reset Password"),(0,t.jsx)(l.lV,{...v,children:(0,t.jsxs)("form",{onSubmit:v.handleSubmit(_),className:"space-y-4 pt-5 px-0 w-full",children:[(0,t.jsx)("h1",{className:"text-[34px] leading-[41px] font-semibold font-poppins text-gray-900 mb-8",children:p}),Object.keys(a).map(e=>(0,t.jsx)(l.zB,{control:v.control,name:e,render:e=>{let{field:r}=e;v.formState.errors[r.name];let a=v.getFieldState(r.name).isTouched,s=w===r.name;return(0,t.jsxs)(l.eI,{className:"flex w-full flex-col gap-3.5",children:[(0,t.jsx)(l.lR,{className:"paragraph-medium text-dark400_light700",children:"email"===r.name?"Email Address":r.name.charAt(0).toUpperCase()+r.name.slice(1)}),(0,t.jsx)(l.MJ,{children:(0,t.jsxs)("div",{className:"relative w-full",children:[(0,t.jsx)(c.p,{type:r.name.toLowerCase().includes("password")&&!P?"password":"text",...r,onFocus:()=>j(r.name),onBlur:()=>j(null),className:"paragraph-regular background-light900_dark300 text-dark300_light700 min-h-12 rounded-1.5 border focus:outline-none w-full ".concat(s?" ":""),placeholder:f[r.name]||""}),r.name.toLowerCase().includes("password")&&(0,t.jsx)("button",{type:"button",onClick:()=>I(!P),className:"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer",children:(0,t.jsx)(h.default,{src:"/images/eye.png",alt:"Toggle Password Visibility",width:20,height:20})})]})}),(0,t.jsx)(l.C5,{})]})}},e)),"SIGN_IN"===d&&(0,t.jsx)("div",{className:"flex justify-end mt-5",children:(0,t.jsx)(u(),{href:"/sign-in/Forgotpassword",className:"font-medium underline text-[#7B61FF] mb-14",children:"Forgot Password?"})}),"SIGN_UP"===d&&(0,t.jsxs)("div",{className:"flex items-start gap-3 text-sm",children:[(0,t.jsx)("input",{type:"checkbox",id:"terms",className:"mt-1",onChange:e=>y(e.target.checked)}),(0,t.jsxs)("label",{htmlFor:"terms",className:"text-gray-700",children:["I agree to all the"," ",(0,t.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Terms"})," of service and"," ",(0,t.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Privacy"})," ",(0,t.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Policies"})]})]}),(0,t.jsx)("div",{className:"flex justify-center w-full",children:(0,t.jsx)(i.$,{className:"primary-button paragraph-medium w-full max-w-[370px] min-h-12 rounded-4xl px-4 py-3 font-inter !text-light-900 text-white hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed",disabled:S||v.formState.isSubmitting||!v.formState.isValid||"SIGN_UP"===d&&!N,children:S||v.formState.isSubmitting?"SIGN_IN"===d?"Signing In...":"SIGN_UP"===d?"Creating Account...":"Processing...":A})}),"SIGN_IN"===d?(0,t.jsxs)("p",{className:"text-center mb-14",children:["Don't have an account? ",(0,t.jsx)(u(),{href:x.SIGN_UP,className:" font-semibold underline text-[#7B61FF]",children:"Sign Up"})]}):(0,t.jsxs)("p",{className:"text-center",children:["Already have an account?"," ",(0,t.jsx)(u(),{href:x.SIGN_IN,className:"underline text-[#6938EF] font-medium ",children:"Login"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-around flex-col gap-2 sm:flex-row sm:gap-0".concat("mt-16"),children:[(0,t.jsx)("span",{className:"text-md text-[#000000]",children:"Or continue with"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("button",{type:"button",className:"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition","aria-label":"Continue with Google",children:(0,t.jsx)(h.default,{src:"/icons/google.svg",alt:"Google",width:32,height:32})}),(0,t.jsx)("button",{type:"button",className:"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition","aria-label":"Continue with Facebook",children:(0,t.jsx)(h.default,{src:"/icons/facebook.svg",alt:"Facebook",width:32,height:32})}),(0,t.jsx)("button",{type:"button",className:"border rounded-full  transition hover:bg-gray-700 hover:cursor-pointer","aria-label":"Continue with Apple",children:(0,t.jsx)(h.default,{src:"/icons/apple.svg",alt:"Apple",width:32,height:32})})]})]})]})})}}}]);