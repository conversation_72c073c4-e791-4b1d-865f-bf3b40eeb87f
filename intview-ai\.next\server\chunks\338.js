exports.id=338,exports.ids=[338],exports.modules={25:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},1279:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});var s=t(687);t(3210);var a=t(218);let n=({children:e,...r})=>(0,s.jsx)(a.N,{...r,children:e})},2704:()=>{},2910:(e,r,t)=>{Promise.resolve().then(t.bind(t,363)),Promise.resolve().then(t.bind(t,25)),Promise.resolve().then(t.bind(t,2175))},2925:(e,r,t)=>{"use strict";t.d(r,{lV:()=>c,MJ:()=>v,zB:()=>u,eI:()=>g,lR:()=>x,C5:()=>b});var s=t(687),a=t(3210),n=t(1391),o=t(7605),i=t(6241),l=t(9467);function d({className:e,...r}){return(0,s.jsx)(l.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}let c=o.Op,m=a.createContext({}),u=({...e})=>(0,s.jsx)(m.Provider,{value:{name:e.name},children:(0,s.jsx)(o.xI,{...e})}),h=()=>{let e=a.useContext(m),r=a.useContext(p),{getFieldState:t}=(0,o.xW)(),s=(0,o.lN)({name:e.name}),n=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}},p=a.createContext({});function g({className:e,...r}){let t=a.useId();return(0,s.jsx)(p.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-3 ",e),...r})})}function x({className:e,...r}){let{error:t,formItemId:a}=h();return(0,s.jsx)(d,{"data-slot":"form-label","data-error":!!t,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...r})}function v({...e}){let{error:r,formItemId:t,formDescriptionId:a,formMessageId:o}=h();return(0,s.jsx)(n.DX,{"data-slot":"form-control",id:t,"aria-describedby":r?`${a} ${o}`:`${a}`,"aria-invalid":!!r,...e})}function b({className:e,...r}){let{error:t,formMessageId:a}=h(),n=t?String(t?.message??""):r.children;return n?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,i.cn)("text-sm !text-red-600",e),...r,children:n}):null}},2977:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var s=t(687),a=t(3210),n=t(3442),o=t(7605),i=t(4934),l=t(2925),d=t(8988),c=t(5814),m=t.n(c),u=t(6189),h=t(2581);let p={HOME:"/",SIGN_IN:"/sign-in",SIGN_UP:"/sign-up"};var g=t(474);let x=({schema:e,defaultValues:r,formType:t,heading:c,placeholderValues:x})=>{let v=(0,u.useRouter)(),b=(0,o.mN)({resolver:(0,n.u)(e),defaultValues:r,mode:"onChange"}),[f,w]=(0,a.useState)(null),[P,j]=(0,a.useState)(!1),[y,N]=(0,a.useState)(!1),[I,S]=(0,a.useState)(!1),k=async e=>{S(!0);try{await new Promise(e=>setTimeout(e,1e3)),h.oR.success("SIGN_IN"===t?"Successfully signed in! Welcome back.":"Account created successfully! Welcome to AI Interview."),setTimeout(()=>{v.push(p.HOME)},1e3)}catch(e){console.error("Authentication error:",e),h.oR.error("SIGN_IN"===t?"Sign in failed. Please check your credentials.":"Account creation failed. Please try again.")}finally{S(!1)}},A="Continue";return"SIGN_IN"===t?A="Sign In":"SIGN_UP"===t?A="Create an Account":"RESET_PASSWORD"===t&&(A="Reset Password"),(0,s.jsx)(l.lV,{...b,children:(0,s.jsxs)("form",{onSubmit:b.handleSubmit(k),className:"space-y-4 pt-5 px-0 w-full",children:[(0,s.jsx)("h1",{className:"text-[34px] leading-[41px] font-semibold font-poppins text-gray-900 mb-8",children:c}),Object.keys(r).map(e=>(0,s.jsx)(l.zB,{control:b.control,name:e,render:({field:e})=>{b.formState.errors[e.name];let r=b.getFieldState(e.name).isTouched,t=f===e.name;return(0,s.jsxs)(l.eI,{className:"flex w-full flex-col gap-3.5",children:[(0,s.jsx)(l.lR,{className:"paragraph-medium text-dark400_light700",children:"email"===e.name?"Email Address":e.name.charAt(0).toUpperCase()+e.name.slice(1)}),(0,s.jsx)(l.MJ,{children:(0,s.jsxs)("div",{className:"relative w-full",children:[(0,s.jsx)(d.p,{type:e.name.toLowerCase().includes("password")&&!y?"password":"text",...e,onFocus:()=>w(e.name),onBlur:()=>w(null),className:`paragraph-regular background-light900_dark300 text-dark300_light700 min-h-12 rounded-1.5 border focus:outline-none w-full ${t?" ":""}`,placeholder:x[e.name]||""}),e.name.toLowerCase().includes("password")&&(0,s.jsx)("button",{type:"button",onClick:()=>N(!y),className:"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer",children:(0,s.jsx)(g.default,{src:"/images/eye.png",alt:"Toggle Password Visibility",width:20,height:20})})]})}),(0,s.jsx)(l.C5,{})]})}},e)),"SIGN_IN"===t&&(0,s.jsx)("div",{className:"flex justify-end mt-5",children:(0,s.jsx)(m(),{href:"/sign-in/Forgotpassword",className:"font-medium underline text-[#7B61FF] mb-14",children:"Forgot Password?"})}),"SIGN_UP"===t&&(0,s.jsxs)("div",{className:"flex items-start gap-3 text-sm",children:[(0,s.jsx)("input",{type:"checkbox",id:"terms",className:"mt-1",onChange:e=>j(e.target.checked)}),(0,s.jsxs)("label",{htmlFor:"terms",className:"text-gray-700",children:["I agree to all the"," ",(0,s.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Terms"})," of service and"," ",(0,s.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Privacy"})," ",(0,s.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Policies"})]})]}),(0,s.jsx)("div",{className:"flex justify-center w-full",children:(0,s.jsx)(i.$,{className:"primary-button paragraph-medium w-full max-w-[370px] min-h-12 rounded-4xl px-4 py-3 font-inter !text-light-900 text-white hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed",disabled:I||b.formState.isSubmitting||!b.formState.isValid||"SIGN_UP"===t&&!P,children:I||b.formState.isSubmitting?"SIGN_IN"===t?"Signing In...":"SIGN_UP"===t?"Creating Account...":"Processing...":A})}),"SIGN_IN"===t?(0,s.jsxs)("p",{className:"text-center mb-14",children:["Don't have an account? ",(0,s.jsx)(m(),{href:p.SIGN_UP,className:" font-semibold underline text-[#7B61FF]",children:"Sign Up"})]}):(0,s.jsxs)("p",{className:"text-center",children:["Already have an account?"," ",(0,s.jsx)(m(),{href:p.SIGN_IN,className:"underline text-[#6938EF] font-medium ",children:"Login"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-around flex-col gap-2 sm:flex-row sm:gap-0mt-16",children:[(0,s.jsx)("span",{className:"text-md text-[#000000]",children:"Or continue with"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{type:"button",className:"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition","aria-label":"Continue with Google",children:(0,s.jsx)(g.default,{src:"/icons/google.svg",alt:"Google",width:32,height:32})}),(0,s.jsx)("button",{type:"button",className:"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition","aria-label":"Continue with Facebook",children:(0,s.jsx)(g.default,{src:"/icons/facebook.svg",alt:"Facebook",width:32,height:32})}),(0,s.jsx)("button",{type:"button",className:"border rounded-full  transition hover:bg-gray-700 hover:cursor-pointer","aria-label":"Continue with Apple",children:(0,s.jsx)(g.default,{src:"/icons/apple.svg",alt:"Apple",width:32,height:32})})]})]})]})})}},4013:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},4593:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>o});var s=t(687),a=t(218),n=t(2581);let o=({...e})=>{let{theme:r="system"}=(0,a.D)();return(0,s.jsx)(n.l$,{theme:r,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},4934:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(687);t(3210);var a=t(1391),n=t(4224),o=t(6241);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:n=!1,...l}){let d=n?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:t,className:e})),...l})}},5958:(e,r,t)=>{Promise.resolve().then(t.bind(t,4593)),Promise.resolve().then(t.bind(t,1279)),Promise.resolve().then(t.bind(t,9208))},6241:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(9384),a=t(2348);function n(...e){return(0,a.QP)((0,s.$)(e))}},6487:()=>{},6749:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},6814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>o,j2:()=>d});var s=t(9859),a=t(3560),n=t(6056);let{handlers:o,signIn:i,signOut:l,auth:d}=(0,s.Ay)({providers:[a.A,n.A],secret:process.env.AUTH_SECRET})},7470:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(7413);let a=({children:e})=>(0,s.jsx)("main",{children:e})},8014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h,metadata:()=>u});var s=t(7413),a=t(363),n=t(6649),o=t.n(n),i=t(5843),l=t.n(i);t(2704);var d=t(25),c=t(2175),m=t(6814);let u={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}},h=async({children:e})=>{let r=await (0,m.j2)();return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)(c.SessionProvider,{session:r,children:(0,s.jsxs)("body",{className:`${o().className} ${l().variable} antialiased`,children:[(0,s.jsx)(d.default,{attribute:"class",defaultTheme:"light",children:e}),(0,s.jsx)(a.Toaster,{})]})})})}},8335:()=>{},8988:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(687);t(3210);var a=t(6241);function n({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},9360:(e,r,t)=>{"use strict";t.d(r,{Ih:()=>o,Il:()=>a,mJ:()=>n,pi:()=>l,zL:()=>i});var s=t(7566);let a=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")}),n=s.Ik({confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),name:s.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")}),o=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."})}),i=s.Ik({newPassword:s.Yj().min(6,{message:"Password must be at least 6 characters long"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Password do not match",path:["confirmPassword"]}),l=s.Ik({otp1:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp2:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp3:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp4:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed")})}};