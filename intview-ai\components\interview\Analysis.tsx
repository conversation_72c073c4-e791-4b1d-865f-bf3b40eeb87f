// import JobInfoCard from "@/components/JobInfoCard";
import QuestionsList from "@/components/QuestionsList";
import InterviewLayout from "@/components/InterviewLayout";
import VideoTranscript from "@/components/VideoTranscript";
import InterviewCard from "@/components/InterviewCard";
import ScoreCard from "../analysis/ScoreCard";
import { useInterview } from "@/context/InterviewContext";
import { useEffect } from "react";

const Analysis = () => {
  const { isInterviewCompleted, videoScores, analyzeVideo, conversationHistory } = useInterview();

  // Automatically analyze video when interview is completed and we have conversation history
  useEffect(() => {
    if (isInterviewCompleted && !videoScores && conversationHistory.length > 0) {
      // Add a small delay to ensure all data is loaded
      const timer = setTimeout(() => {
        analyzeVideo();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isInterviewCompleted, videoScores, conversationHistory.length, analyzeVideo]);
  return (
    <div className="h-screen">
      <InterviewCard />
      <InterviewLayout>
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start">
          <QuestionsList />
          {/* <CandidateWithAgent
            className="h-[490px]"
            useAgent={false} 
            candidateName="Jonathan"
            jobTitle="Insurance Agent"
          /> */}
          <VideoTranscript />
        </div>
      </InterviewLayout>
      <ScoreCard />
    </div>
  );
};

export default Analysis;
