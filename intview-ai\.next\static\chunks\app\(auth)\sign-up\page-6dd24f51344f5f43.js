(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{2257:(e,l,s)=>{"use strict";s.r(l),s.d(l,{default:()=>m});var a=s(5155);s(2115);var r=s(6766),i=s(9858),d=s(3582);let m=()=>{let e={email:"Enter your email address",name:"Enter your full name",password:"Enter your password",confirmPassword:"Confirm your password"};return(0,a.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)(r.default,{src:"/images/bgauthimg.png",alt:"auth",fill:!0,className:"object-cover",priority:!0})}),(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,a.jsx)(r.default,{src:"/images/bgauthimg.png",alt:"auth",fill:!0,className:"object-cover",priority:!0})}),(0,a.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,a.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,a.jsx)(i.A,{formType:"SIGN_UP",schema:d.mJ,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:!0,data:e}),heading:"Sign Up - For Applicants",placeholderValues:e})})})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,a.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,a.jsx)("div",{className:"w-full max-w-md",children:(0,a.jsx)(i.A,{formType:"SIGN_UP",schema:d.mJ,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:!0,data:e}),heading:"Sign Up - For Applicants",placeholderValues:e})})})})})]})}},5217:(e,l,s)=>{Promise.resolve().then(s.bind(s,2257))}},e=>{var l=l=>e(e.s=l);e.O(0,[766,831,903,671,874,421,441,684,358],()=>l(5217)),_N_E=e.O()}]);