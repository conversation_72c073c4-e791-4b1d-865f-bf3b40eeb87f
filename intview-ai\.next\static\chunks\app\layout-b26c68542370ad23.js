(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{1362:(e,t,s)=>{"use strict";s.d(t,{D:()=>c,N:()=>d});var n=s(2115),r=(e,t,s,n,r,a,o,i)=>{let l=document.documentElement,c=["light","dark"];function d(t){var s;(Array.isArray(e)?e:[e]).forEach(e=>{let s="class"===e,n=s&&a?r.map(e=>a[e]||e):r;s?(l.classList.remove(...n),l.classList.add(a&&a[t]?a[t]:t)):l.setAttribute(e,t)}),s=t,i&&c.includes(s)&&(l.style.colorScheme=s)}if(n)d(n);else try{let e=localStorage.getItem(t)||s,n=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(n)}catch(e){}},a=["light","dark"],o="(prefers-color-scheme: dark)",i=n.createContext(void 0),l={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=n.useContext(i))?e:l},d=e=>n.useContext(i)?n.createElement(n.Fragment,null,e.children):n.createElement(m,{...e}),u=["light","dark"],m=e=>{let{forcedTheme:t,disableTransitionOnChange:s=!1,enableSystem:r=!0,enableColorScheme:l=!0,storageKey:c="theme",themes:d=u,defaultTheme:m=r?"system":"light",attribute:f="data-theme",value:g,children:E,nonce:b,scriptProps:S}=e,[_,x]=n.useState(()=>p(c,m)),[w,k]=n.useState(()=>"system"===_?y():_),L=g?Object.values(g):d,T=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&r&&(t=y());let n=g?g[t]:t,o=s?v(b):null,i=document.documentElement,c=e=>{"class"===e?(i.classList.remove(...L),n&&i.classList.add(n)):e.startsWith("data-")&&(n?i.setAttribute(e,n):i.removeAttribute(e))};if(Array.isArray(f)?f.forEach(c):c(f),l){let e=a.includes(m)?m:null,s=a.includes(t)?t:e;i.style.colorScheme=s}null==o||o()},[b]),A=n.useCallback(e=>{let t="function"==typeof e?e(_):e;x(t);try{localStorage.setItem(c,t)}catch(e){}},[_]),C=n.useCallback(e=>{k(y(e)),"system"===_&&r&&!t&&T("system")},[_,t]);n.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(C),C(e),()=>e.removeListener(C)},[C]),n.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?x(e.newValue):A(m))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[A]),n.useEffect(()=>{T(null!=t?t:_)},[t,_]);let N=n.useMemo(()=>({theme:_,setTheme:A,forcedTheme:t,resolvedTheme:"system"===_?w:_,themes:r?[...d,"system"]:d,systemTheme:r?w:void 0}),[_,A,t,w,r,d]);return n.createElement(i.Provider,{value:N},n.createElement(h,{forcedTheme:t,storageKey:c,attribute:f,enableSystem:r,enableColorScheme:l,defaultTheme:m,value:g,themes:d,nonce:b,scriptProps:S}),E)},h=n.memo(e=>{let{forcedTheme:t,storageKey:s,attribute:a,enableSystem:o,enableColorScheme:i,defaultTheme:l,value:c,themes:d,nonce:u,scriptProps:m}=e,h=JSON.stringify([a,s,l,t,d,c,o,i]).slice(1,-1);return n.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(r.toString(),")(").concat(h,")")}})}),p=(e,t)=>{let s;try{s=localStorage.getItem(e)||void 0}catch(e){}return s||t},v=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},4993:e=>{e.exports={style:{fontFamily:"'spaceGrotesk', 'spaceGrotesk Fallback'"},className:"__className_fc28aa",variable:"__variable_fc28aa"}},5493:(e,t,s)=>{"use strict";s.d(t,{SessionProvider:()=>ea});var n,r,a,o,i,l=s(5155),c=s(2115),d=s.t(c,2);class u extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let s=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${s}`}}class m extends u{}m.kind="signIn";class h extends u{}h.type="AdapterError";class p extends u{}p.type="AccessDenied";class v extends u{}v.type="CallbackRouteError";class y extends u{}y.type="ErrorPageLoop";class f extends u{}f.type="EventError";class g extends u{}g.type="InvalidCallbackUrl";class E extends m{constructor(){super(...arguments),this.code="credentials"}}E.type="CredentialsSignin";class b extends u{}b.type="InvalidEndpoints";class S extends u{}S.type="InvalidCheck";class _ extends u{}_.type="JWTSessionError";class x extends u{}x.type="MissingAdapter";class w extends u{}w.type="MissingAdapterMethods";class k extends u{}k.type="MissingAuthorize";class L extends u{}L.type="MissingSecret";class T extends m{}T.type="OAuthAccountNotLinked";class A extends m{}A.type="OAuthCallbackError";class C extends u{}C.type="OAuthProfileParseError";class N extends u{}N.type="SessionTokenError";class P extends m{}P.type="OAuthSignInError";class U extends m{}U.type="EmailSignInError";class R extends u{}R.type="SignOutError";class M extends u{}M.type="UnknownAction";class I extends u{}I.type="UnsupportedStrategy";class O extends u{}O.type="InvalidProvider";class H extends u{}H.type="UntrustedHost";class F extends u{}F.type="Verification";class j extends m{}j.type="MissingCSRF";class W extends u{}W.type="DuplicateConditionalUI";class V extends u{}V.type="MissingWebAuthnAutocomplete";class X extends u{}X.type="WebAuthnVerificationError";class D extends m{}D.type="AccountNotLinked";class $ extends u{}$.type="ExperimentalFeatureNotEnabled";class J extends u{}class K extends u{}async function z(e,t,s){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};let r="".concat(t.basePath,"/").concat(e);try{var a;let e={headers:{"Content-Type":"application/json",...(null==n||null==(a=n.headers)?void 0:a.cookie)?{cookie:n.headers.cookie}:{}}};(null==n?void 0:n.body)&&(e.body=JSON.stringify(n.body),e.method="POST");let t=await fetch(r,e),s=await t.json();if(!t.ok)throw s;return s}catch(e){return s.error(new J(e.message,e)),null}}function B(){return Math.floor(Date.now()/1e3)}function G(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e="https://".concat(e));let s=new URL(e||t),n=("/"===s.pathname?t.pathname:s.pathname).replace(/\/$/,""),r="".concat(s.origin).concat(n);return{origin:s.origin,host:s.host,path:n,base:r,toString:()=>r}}var q=s(9509);let Q={baseUrl:G(null!=(r=q.env.NEXTAUTH_URL)?r:q.env.VERCEL_URL).origin,basePath:G(q.env.NEXTAUTH_URL).path,baseUrlServer:G(null!=(o=null!=(a=q.env.NEXTAUTH_URL_INTERNAL)?a:q.env.NEXTAUTH_URL)?o:q.env.VERCEL_URL).origin,basePathServer:G(null!=(i=q.env.NEXTAUTH_URL_INTERNAL)?i:q.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},Y=null;function Z(){return"undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{},name:"next-auth",onmessage:null,onmessageerror:null,close:()=>{},dispatchEvent:()=>!1}:new BroadcastChannel("next-auth")}function ee(){return null===Y&&(Y=Z()),Y}let et={debug:console.debug,error:console.error,warn:console.warn},es=null==(n=c.createContext)?void 0:n.call(d,void 0);async function en(e){var t;let s=await z("session",Q,et,e);return(null==(t=null==e?void 0:e.broadcast)||t)&&Z().postMessage({event:"session",data:{trigger:"getSession"}}),s}async function er(){var e;let t=await z("csrf",Q,et);return null!=(e=null==t?void 0:t.csrfToken)?e:""}function ea(e){if(!es)throw Error("React Context is unavailable in Server Components");let{children:t,basePath:s,refetchInterval:n,refetchWhenOffline:r}=e;s&&(Q.basePath=s);let a=void 0!==e.session;Q._lastSync=a?B():0;let[o,i]=c.useState(()=>(a&&(Q._session=e.session),e.session)),[d,u]=c.useState(!a);c.useEffect(()=>(Q._getSession=async function(){let{event:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t="storage"===e;if(t||void 0===Q._session){Q._lastSync=B(),Q._session=await en({broadcast:!t}),i(Q._session);return}if(!e||null===Q._session||B()<Q._lastSync)return;Q._lastSync=B(),Q._session=await en(),i(Q._session)}catch(e){et.error(new K(e.message,e))}finally{u(!1)}},Q._getSession(),()=>{Q._lastSync=0,Q._session=void 0,Q._getSession=()=>{}}),[]),c.useEffect(()=>{let e=()=>Q._getSession({event:"storage"});return ee().addEventListener("message",e),()=>ee().removeEventListener("message",e)},[]),c.useEffect(()=>{let{refetchOnWindowFocus:t=!0}=e,s=()=>{t&&"visible"===document.visibilityState&&Q._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",s,!1),()=>document.removeEventListener("visibilitychange",s,!1)},[e.refetchOnWindowFocus]);let m=function(){let[e,t]=c.useState("undefined"!=typeof navigator&&navigator.onLine),s=()=>t(!0),n=()=>t(!1);return c.useEffect(()=>(window.addEventListener("online",s),window.addEventListener("offline",n),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",n)}),[]),e}(),h=!1!==r||m;c.useEffect(()=>{if(n&&h){let e=setInterval(()=>{Q._session&&Q._getSession({event:"poll"})},1e3*n);return()=>clearInterval(e)}},[n,h]);let p=c.useMemo(()=>({data:o,status:d?"loading":o?"authenticated":"unauthenticated",async update(e){if(d)return;u(!0);let t=await z("session",Q,et,void 0===e?void 0:{body:{csrfToken:await er(),data:e}});return u(!1),t&&(i(t),ee().postMessage({event:"session",data:{trigger:"getSession"}})),t}}),[o,d]);return(0,l.jsx)(es.Provider,{value:p,children:t})}},5851:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>o});var n=s(5155),r=s(1362),a=s(6671);let o=e=>{let{...t}=e,{theme:s="system"}=(0,r.D)();return(0,n.jsx)(a.l$,{theme:s,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})}},8322:(e,t,s)=>{Promise.resolve().then(s.bind(s,5851)),Promise.resolve().then(s.bind(s,8603)),Promise.resolve().then(s.bind(s,5493)),Promise.resolve().then(s.t.bind(s,9623,23)),Promise.resolve().then(s.t.bind(s,4993,23)),Promise.resolve().then(s.t.bind(s,9324,23))},8603:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var n=s(5155);s(2115);var r=s(1362);let a=e=>{let{children:t,...s}=e;return(0,n.jsx)(r.N,{...s,children:t})}},9324:()=>{},9623:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback', Helvetica, Arial, sans-serif",fontStyle:"normal"},className:"__className_9b9fd1",variable:"__variable_9b9fd1"}}},e=>{var t=t=>e(e.s=t);e.O(0,[399,671,441,684,358],()=>t(8322)),_N_E=e.O()}]);