(()=>{var e={};e.id=76,e.ids=[76],e.modules={606:(e,t,r)=>{Promise.resolve().then(r.bind(r,9813))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},870:(e,t,r)=>{Promise.resolve().then(r.bind(r,8743))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5511:e=>{"use strict";e.exports=require("crypto")},7041:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var o=r(5239),s=r(8088),i=r(8170),n=r.n(i),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["(root)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8743)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2528)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(root)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8743:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(root)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9813:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(687);let s=()=>(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Dashboard"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Welcome to AI Interview"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Get started with your interview preparation."})]}),(0,o.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Job Posts"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Manage and view your job applications."})]}),(0,o.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Analytics"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Track your interview performance."})]})]})]})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[97,423,598,814,762],()=>r(7041));module.exports=o})();