const ScoreBar = ({ label, value, color = "bg-orange-500", loading = false }) => {
  return (
    <div className="mb-2">
      <div className="flex justify-between text-sm mb-1">
        <span className="mb-1">{label}</span>
        <span>{loading ? '...' : `${value}/100`}</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2.5">
        {loading ? (
          <div className="h-2.5 rounded-full bg-gray-300 animate-pulse"></div>
        ) : (
          <div
            className={`h-2.5 rounded-full ${color}`}
            style={{ width: `${value}%` }}
          ></div>
        )}
      </div>
    </div>
  );
};

export default ScoreBar;
