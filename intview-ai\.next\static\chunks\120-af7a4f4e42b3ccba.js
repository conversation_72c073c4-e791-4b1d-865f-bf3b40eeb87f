(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[120],{646:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},736:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=function(t){return t&&t.__esModule?t:{default:t}}(n(5501)),i=n(4783),o=n(9070);function a(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){n(t);return}s.done?e(c):Promise.resolve(c).then(r,i)}function s(t){return function(){var e=this,n=arguments;return new Promise(function(r,i){var o=t.apply(e,n);function s(t){a(o,r,i,s,c,"next",t)}function c(t){a(o,r,i,s,c,"throw",t)}s(void 0)})}}e.default=function(){var t,e,n,a,c;function u(t){if(!(this instanceof u))throw TypeError("Cannot call a class as a function");this.recognition=null,this.pauseAfterDisconnect=!1,this.interimTranscript="",this.finalTranscript="",this.listening=!1,this.isMicrophoneAvailable=!0,this.subscribers={},this.onStopListening=function(){},this.previousResultWasFinalOnly=!1,this.resetTranscript=this.resetTranscript.bind(this),this.startListening=this.startListening.bind(this),this.stopListening=this.stopListening.bind(this),this.abortListening=this.abortListening.bind(this),this.setSpeechRecognition=this.setSpeechRecognition.bind(this),this.disableRecognition=this.disableRecognition.bind(this),this.setSpeechRecognition(t),(0,r.default)()&&(this.updateFinalTranscript=(0,i.debounce)(this.updateFinalTranscript,250,!0))}return c=[{key:"setSpeechRecognition",value:function(t){var e=!!t&&((0,o.isNative)(t)||(0,i.browserSupportsPolyfills)());e&&(this.disableRecognition(),this.recognition=new t,this.recognition.continuous=!1,this.recognition.interimResults=!0,this.recognition.onresult=this.updateTranscript.bind(this),this.recognition.onend=this.onRecognitionDisconnect.bind(this),this.recognition.onerror=this.onError.bind(this)),this.emitBrowserSupportsSpeechRecognitionChange(e)}},{key:"subscribe",value:function(t,e){this.subscribers[t]=e}},{key:"unsubscribe",value:function(t){delete this.subscribers[t]}},{key:"emitListeningChange",value:function(t){var e=this;this.listening=t,Object.keys(this.subscribers).forEach(function(n){(0,e.subscribers[n].onListeningChange)(t)})}},{key:"emitMicrophoneAvailabilityChange",value:function(t){var e=this;this.isMicrophoneAvailable=t,Object.keys(this.subscribers).forEach(function(n){(0,e.subscribers[n].onMicrophoneAvailabilityChange)(t)})}},{key:"emitTranscriptChange",value:function(t,e){var n=this;Object.keys(this.subscribers).forEach(function(r){(0,n.subscribers[r].onTranscriptChange)(t,e)})}},{key:"emitClearTranscript",value:function(){var t=this;Object.keys(this.subscribers).forEach(function(e){(0,t.subscribers[e].onClearTranscript)()})}},{key:"emitBrowserSupportsSpeechRecognitionChange",value:function(t){var e=this;Object.keys(this.subscribers).forEach(function(n){var r=e.subscribers[n],i=r.onBrowserSupportsSpeechRecognitionChange,o=r.onBrowserSupportsContinuousListeningChange;i(t),o(t)})}},{key:"disconnect",value:function(t){if(this.recognition&&this.listening)switch(t){case"ABORT":this.pauseAfterDisconnect=!0,this.abort();break;case"RESET":this.pauseAfterDisconnect=!1,this.abort();break;default:this.pauseAfterDisconnect=!0,this.stop()}}},{key:"disableRecognition",value:function(){this.recognition&&(this.recognition.onresult=function(){},this.recognition.onend=function(){},this.recognition.onerror=function(){},this.listening&&this.stopListening())}},{key:"onError",value:function(t){t&&t.error&&"not-allowed"===t.error&&(this.emitMicrophoneAvailabilityChange(!1),this.disableRecognition())}},{key:"onRecognitionDisconnect",value:function(){this.onStopListening(),this.listening=!1,this.pauseAfterDisconnect?this.emitListeningChange(!1):this.recognition&&(this.recognition.continuous?this.startListening({continuous:this.recognition.continuous}):this.emitListeningChange(!1)),this.pauseAfterDisconnect=!1}},{key:"updateTranscript",value:function(t){var e=t.results,n=t.resultIndex,o=void 0===n?e.length-1:n;this.interimTranscript="",this.finalTranscript="";for(var a=o;a<e.length;++a)e[a].isFinal&&(!(0,r.default)()||e[a][0].confidence>0)?this.updateFinalTranscript(e[a][0].transcript):this.interimTranscript=(0,i.concatTranscripts)(this.interimTranscript,e[a][0].transcript);var s=!1;""===this.interimTranscript&&""!==this.finalTranscript?(this.previousResultWasFinalOnly&&(s=!0),this.previousResultWasFinalOnly=!0):this.previousResultWasFinalOnly=!1,s||this.emitTranscriptChange(this.interimTranscript,this.finalTranscript)}},{key:"updateFinalTranscript",value:function(t){this.finalTranscript=(0,i.concatTranscripts)(this.finalTranscript,t)}},{key:"resetTranscript",value:function(){this.disconnect("RESET")}},{key:"startListening",value:(t=s(regeneratorRuntime.mark(function t(){var e,n,r,i,o,a,s=arguments;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r=void 0!==(n=(e=s.length>0&&void 0!==s[0]?s[0]:{}).continuous)&&n,i=e.language,this.recognition){t.next=3;break}return t.abrupt("return");case 3:if(o=r!==this.recognition.continuous,a=i&&i!==this.recognition.lang,!(o||a)){t.next=11;break}if(!this.listening){t.next=9;break}return t.next=9,this.stopListening();case 9:this.recognition.continuous=o?r:this.recognition.continuous,this.recognition.lang=a?i:this.recognition.lang;case 11:if(this.listening){t.next=22;break}return this.recognition.continuous||(this.resetTranscript(),this.emitClearTranscript()),t.prev=13,t.next=16,this.start();case 16:this.emitListeningChange(!0),t.next=22;break;case 19:t.prev=19,t.t0=t.catch(13),t.t0 instanceof DOMException||this.emitMicrophoneAvailabilityChange(!1);case 22:case"end":return t.stop()}},t,this,[[13,19]])})),function(){return t.apply(this,arguments)})},{key:"abortListening",value:(e=s(regeneratorRuntime.mark(function t(){var e=this;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return this.disconnect("ABORT"),this.emitListeningChange(!1),t.next=4,new Promise(function(t){e.onStopListening=t});case 4:case"end":return t.stop()}},t,this)})),function(){return e.apply(this,arguments)})},{key:"stopListening",value:(n=s(regeneratorRuntime.mark(function t(){var e=this;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return this.disconnect("STOP"),this.emitListeningChange(!1),t.next=4,new Promise(function(t){e.onStopListening=t});case 4:case"end":return t.stop()}},t,this)})),function(){return n.apply(this,arguments)})},{key:"getRecognition",value:function(){return this.recognition}},{key:"start",value:(a=s(regeneratorRuntime.mark(function t(){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(this.recognition&&!this.listening)){t.next=4;break}return t.next=3,this.recognition.start();case 3:this.listening=!0;case 4:case"end":return t.stop()}},t,this)})),function(){return a.apply(this,arguments)})},{key:"stop",value:function(){this.recognition&&this.listening&&(this.recognition.stop(),this.listening=!1)}},{key:"abort",value:function(){this.recognition&&this.listening&&(this.recognition.abort(),this.listening=!1)}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}(u.prototype,c),u}()},839:()=>{},2138:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2551:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.transcriptReducer=void 0;var r=n(6199),i=n(4783);e.transcriptReducer=function(t,e){switch(e.type){case r.CLEAR_TRANSCRIPT:return{interimTranscript:"",finalTranscript:""};case r.APPEND_TRANSCRIPT:return{interimTranscript:e.payload.interimTranscript,finalTranscript:(0,i.concatTranscripts)(t.finalTranscript,e.payload.finalTranscript)};default:throw Error()}}},3393:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.appendTranscript=e.clearTranscript=void 0;var r=n(6199);e.clearTranscript=function(){return{type:r.CLEAR_TRANSCRIPT}},e.appendTranscript=function(t,e){return{type:r.APPEND_TRANSCRIPT,payload:{interimTranscript:t,finalTranscript:e}}}},4516:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4783:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.browserSupportsPolyfills=e.compareTwoStringsUsingDiceCoefficient=e.commandToRegExp=e.concatTranscripts=e.debounce=void 0,e.debounce=function(t,e,n){var r;return function(){var i=this,o=arguments,a=n&&!r;clearTimeout(r),r=setTimeout(function(){r=null,n||t.apply(i,o)},e),a&&t.apply(i,o)}},e.concatTranscripts=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.map(function(t){return t.trim()}).join(" ").trim()};var n=/\s*\((.*?)\)\s*/g,r=/(\(\?:[^)]+\))\?/g,i=/(\(\?)?:\w+/g,o=/\*/g,a=/[-{}[\]+?.,\\^$|#]/g;e.commandToRegExp=function(t){return t instanceof RegExp?RegExp(t.source,"i"):RegExp("^"+(t=t.replace(a,"\\$&").replace(n,"(?:$1)?").replace(i,function(t,e){return e?t:"([^\\s]+)"}).replace(o,"(.*?)").replace(r,"\\s*$1?\\s*"))+"$","i")},e.compareTwoStringsUsingDiceCoefficient=function(t,e){if(t=t.replace(/\s+/g,"").toLowerCase(),e=e.replace(/\s+/g,"").toLowerCase(),!t.length&&!e.length)return 1;if(!t.length||!e.length)return 0;if(t===e)return 1;if(1===t.length&&1===e.length||t.length<2||e.length<2)return 0;for(var n=new Map,r=0;r<t.length-1;r++){var i=t.substring(r,r+2),o=n.has(i)?n.get(i)+1:1;n.set(i,o)}for(var a=0,s=0;s<e.length-1;s++){var c=e.substring(s,s+2),u=n.has(c)?n.get(c):0;u>0&&(n.set(c,u-1),a++)}return 2*a/(t.length+e.length-2)},e.browserSupportsPolyfills=function(){return"undefined"!=typeof window&&void 0!==window.navigator&&void 0!==window.navigator.mediaDevices&&void 0!==window.navigator.mediaDevices.getUserMedia&&(void 0!==window.AudioContext||void 0!==window.webkitAudioContext)}},5133:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("keyboard",[["path",{d:"M10 8h.01",key:"1r9ogq"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M14 8h.01",key:"1primd"}],["path",{d:"M16 12h.01",key:"1l6xoz"}],["path",{d:"M18 8h.01",key:"emo2bl"}],["path",{d:"M6 8h.01",key:"x9i8wu"}],["path",{d:"M7 16h10",key:"wp8him"}],["path",{d:"M8 12h.01",key:"czm47f"}],["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}]])},5501:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default=function(){return/(android)/i.test("undefined"!=typeof navigator?navigator.userAgent:"")}},5925:t=>{var e=function(t){"use strict";var e,n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,n,r,o){var a,s,c,u,l=Object.create((n&&n.prototype instanceof y?n:y).prototype);return i(l,"_invoke",{value:(a=t,s=r,c=new L(o||[]),u=p,function(t,n){if(u===f)throw Error("Generator is already running");if(u===g){if("throw"===t)throw n;return{value:e,done:!0}}for(c.method=t,c.arg=n;;){var r=c.delegate;if(r){var i=function t(n,r){var i=r.method,o=n.iterator[i];if(e===o)return(r.delegate=null,"throw"===i&&n.iterator.return&&(r.method="return",r.arg=e,t(n,r),"throw"===r.method))?d:("return"!==i&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+i+"' method")),d);var a=h(o,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,d;var s=a.arg;return s?s.done?(r[n.resultName]=s.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,d):s:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,d)}(r,c);if(i){if(i===d)continue;return i}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if(u===p)throw u=g,c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);u=f;var o=h(a,s,c);if("normal"===o.type){if(u=c.done?g:"suspendedYield",o.arg===d)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(u=g,c.method="throw",c.arg=o.arg)}})}),l}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var p="suspendedStart",f="executing",g="completed",d={};function y(){}function v(){}function m(){}var b={};u(b,a,function(){return this});var w=Object.getPrototypeOf,k=w&&w(w(A([])));k&&k!==n&&r.call(k,a)&&(b=k);var R=m.prototype=y.prototype=Object.create(b);function T(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function x(t,e){var n;i(this,"_invoke",{value:function(i,o){function a(){return new e(function(n,a){!function n(i,o,a,s){var c=h(t[i],t,o);if("throw"===c.type)s(c.arg);else{var u=c.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,s)},function(t){n("throw",t,a,s)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,s)})}}(i,o,n,a)})}return n=n?n.then(a,a):a()}})}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function A(t){if(null!=t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw TypeError(typeof t+" is not iterable")}return v.prototype=m,i(R,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:v,configurable:!0}),v.displayName=u(m,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,c,"GeneratorFunction")),t.prototype=Object.create(R),t},t.awrap=function(t){return{__await:t}},T(x.prototype),u(x.prototype,s,function(){return this}),t.AsyncIterator=x,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new x(l(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},T(R),u(R,c,"Generator"),u(R,a,function(){return this}),u(R,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=A,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return s.type="throw",s.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);else if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else if(u){if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else throw Error("try statement without catch or finally")}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return(a.type=t,a.arg=e,o)?(this.method="next",this.next=o.finallyLoc,d):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;C(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:A(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),d}},t}(t.exports);try{regeneratorRuntime=e}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}},6199:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND_TRANSCRIPT=e.CLEAR_TRANSCRIPT=void 0,e.CLEAR_TRANSCRIPT="CLEAR_TRANSCRIPT",e.APPEND_TRANSCRIPT="APPEND_TRANSCRIPT"},6325:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("briefcase-business",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},6636:(t,e,n)=>{"use strict";n.d(e,{Hf:()=>s,QF:()=>a});var r=n(2115),i=function(t,e){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)};function o(t){var e,n,i,o,a,s,c,u,l=t.className,h=t.counterClockwise,p=t.dashRatio,f=t.pathRadius,g=t.strokeWidth,d=t.style;return(0,r.createElement)("path",{className:l,style:Object.assign({},d,(n=(e={pathRadius:f,dashRatio:p,counterClockwise:h}).counterClockwise,i=e.dashRatio,a=(1-i)*(o=2*Math.PI*e.pathRadius),{strokeDasharray:o+"px "+o+"px",strokeDashoffset:(n?-a:a)+"px"})),d:(c=(s={pathRadius:f,counterClockwise:h}).pathRadius,"\n      M 50,50\n      m 0,-"+c+"\n      a "+c+","+c+" "+(u=+!!s.counterClockwise)+" 1 1 0,"+2*c+"\n      a "+c+","+c+" "+u+" 1 1 0,-"+2*c+"\n    "),strokeWidth:g,fillOpacity:0})}var a=function(t){function e(){this.constructor=n}function n(){return null!==t&&t.apply(this,arguments)||this}return i(n,t),n.prototype=null===t?Object.create(t):(e.prototype=t.prototype,new e),n.prototype.getBackgroundPadding=function(){return this.props.background?this.props.backgroundPadding:0},n.prototype.getPathRadius=function(){return 50-this.props.strokeWidth/2-this.getBackgroundPadding()},n.prototype.getPathRatio=function(){var t=this.props,e=t.value,n=t.minValue,r=t.maxValue;return(Math.min(Math.max(e,n),r)-n)/(r-n)},n.prototype.render=function(){var t=this.props,e=t.circleRatio,n=t.className,i=t.classes,a=t.counterClockwise,s=t.styles,c=t.strokeWidth,u=t.text,l=this.getPathRadius(),h=this.getPathRatio();return(0,r.createElement)("svg",{className:i.root+" "+n,style:s.root,viewBox:"0 0 100 100","data-test-id":"CircularProgressbar"},this.props.background?(0,r.createElement)("circle",{className:i.background,style:s.background,cx:50,cy:50,r:50}):null,(0,r.createElement)(o,{className:i.trail,counterClockwise:a,dashRatio:e,pathRadius:l,strokeWidth:c,style:s.trail}),(0,r.createElement)(o,{className:i.path,counterClockwise:a,dashRatio:h*e,pathRadius:l,strokeWidth:c,style:s.path}),u?(0,r.createElement)("text",{className:i.text,style:s.text,x:50,y:50},u):null)},n.defaultProps={background:!1,backgroundPadding:0,circleRatio:1,classes:{root:"CircularProgressbar",trail:"CircularProgressbar-trail",path:"CircularProgressbar-path",text:"CircularProgressbar-text",background:"CircularProgressbar-background"},counterClockwise:!1,className:"",maxValue:100,minValue:0,strokeWidth:8,styles:{root:{},trail:{},path:{},text:{},background:{}},text:""},n}(r.Component);function s(t){var e=t.rotation,n=t.strokeLinecap,r=t.textColor,i=t.textSize,o=t.pathColor,a=t.pathTransition,s=t.pathTransitionDuration,u=t.trailColor,l=t.backgroundColor,h=null==e?void 0:"rotate("+e+"turn)",p=null==e?void 0:"center center";return{root:{},path:c({stroke:o,strokeLinecap:n,transform:h,transformOrigin:p,transition:a,transitionDuration:null==s?void 0:s+"s"}),trail:c({stroke:u,strokeLinecap:n,transform:h,transformOrigin:p}),text:c({fill:r,fontSize:i}),background:c({fill:l})}}function c(t){return Object.keys(t).forEach(function(e){null==t[e]&&delete t[e]}),t}},6866:(t,e,n)=>{"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useSpeechRecognition",{enumerable:!0,get:function(){return i.useSpeechRecognition}}),e.default=void 0;var i=function(t){if(t&&t.__esModule)return t;if(null===t||"object"!==r(t)&&"function"!=typeof t)return{default:t};var e=o();if(e&&e.has(t))return e.get(t);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){var s=i?Object.getOwnPropertyDescriptor(t,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=t[a]}return n.default=t,e&&e.set(t,n),n}(n(8127));function o(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return o=function(){return t},t}e.default=i.default},8127:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.useSpeechRecognition=void 0;var r,i=n(2115),o=n(4783),a=n(3393),s=n(2551),c=h(n(736)),u=h(n(5501)),l=h(n(9070));function h(t){return t&&t.__esModule?t:{default:t}}function p(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){n(t);return}s.done?e(c):Promise.resolve(c).then(r,i)}function f(t){return function(){var e=this,n=arguments;return new Promise(function(r,i){var o=t.apply(e,n);function a(t){p(o,r,i,a,s,"next",t)}function s(t){p(o,r,i,a,s,"throw",t)}a(void 0)})}}function g(t){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(t){i=!0,o=t}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}}(t,e)||y(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){if(t){if("string"==typeof t)return v(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(t,e)}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var m=!!l.default,b=m&&!(0,u.default)();e.useSpeechRecognition=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.transcribing,n=void 0===e||e,r=t.clearTranscriptOnListen,c=void 0===r||r,u=t.commands,l=void 0===u?[]:u,h=d((0,i.useState)(w.getRecognitionManager()),1)[0],p=d((0,i.useState)(m),2),f=p[0],k=p[1],R=d((0,i.useState)(b),2),T=R[0],x=R[1],S=d((0,i.useReducer)(s.transcriptReducer,{interimTranscript:h.interimTranscript,finalTranscript:""}),2),C=S[0],L=C.interimTranscript,A=C.finalTranscript,P=S[1],E=d((0,i.useState)(h.listening),2),O=E[0],_=E[1],M=d((0,i.useState)(h.isMicrophoneAvailable),2),j=M[0],N=M[1],D=(0,i.useRef)(l);D.current=l;var I=function(){P((0,a.clearTranscript)())},F=(0,i.useCallback)(function(){h.resetTranscript(),I()},[h]),W=function(t,e,n){var r=("object"===g(t)?t.toString():t).replace(/[&/\\#,+()!$~%.'":*?<>{}]/g,"").replace(/  +/g," ").trim(),i=(0,o.compareTwoStringsUsingDiceCoefficient)(r,e);return i>=n?{command:t,commandWithoutSpecials:r,howSimilar:i,isFuzzyMatch:!0}:null},z=function(t,e){var n=(0,o.commandToRegExp)(t).exec(e);return n?{command:t,parameters:n.slice(1)}:null},B=(0,i.useCallback)(function(t,e){D.current.forEach(function(n){var r=n.command,i=n.callback,o=n.matchInterim,a=n.isFuzzyMatch,s=void 0!==a&&a,c=n.fuzzyMatchingThreshold,u=void 0===c?.8:c,l=n.bestMatchOnly,h=!e&&void 0!==o&&o?t.trim():e.trim(),p=(Array.isArray(r)?r:[r]).map(function(t){return s?W(t,h,u):z(t,h)}).filter(function(t){return t});if(s&&void 0!==l&&l&&p.length>=2){p.sort(function(t,e){return e.howSimilar-t.howSimilar});var f=p[0],g=f.command;i(f.commandWithoutSpecials,h,f.howSimilar,{command:g,resetTranscript:F})}else p.forEach(function(t){if(t.isFuzzyMatch){var e=t.command;i(t.commandWithoutSpecials,h,t.howSimilar,{command:e,resetTranscript:F})}else{var n=t.command,r=t.parameters;i.apply(void 0,((function(t){if(Array.isArray(t))return v(t)})(r)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(r)||y(r)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat([{command:n,resetTranscript:F}]))}})})},[F]),$=(0,i.useCallback)(function(t,e){n&&P((0,a.appendTranscript)(t,e)),B(t,e)},[B,n]),G=(0,i.useCallback)(function(){c&&I()},[c]);return(0,i.useEffect)(function(){var t=w.counter;return w.counter+=1,h.subscribe(t,{onListeningChange:_,onMicrophoneAvailabilityChange:N,onTranscriptChange:$,onClearTranscript:G,onBrowserSupportsSpeechRecognitionChange:k,onBrowserSupportsContinuousListeningChange:x}),function(){h.unsubscribe(t)}},[n,c,h,$,G]),{transcript:(0,o.concatTranscripts)(A,L),interimTranscript:L,finalTranscript:A,listening:O,isMicrophoneAvailable:j,resetTranscript:F,browserSupportsSpeechRecognition:f,browserSupportsContinuousListening:T}};var w={counter:0,applyPolyfill:function(t){r?r.setSpeechRecognition(t):r=new c.default(t);var e=!!t&&(0,o.browserSupportsPolyfills)();m=e,b=e},removePolyfill:function(){r?r.setSpeechRecognition(l.default):r=new c.default(l.default),b=(m=!!l.default)&&!(0,u.default)()},getRecognitionManager:function(){return r||(r=new c.default(l.default)),r},getRecognition:function(){return w.getRecognitionManager().getRecognition()},startListening:function(){var t=f(regeneratorRuntime.mark(function t(){var e,n,r,i,o=arguments;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n=(e=o.length>0&&void 0!==o[0]?o[0]:{}).continuous,r=e.language,i=w.getRecognitionManager(),t.next=4,i.startListening({continuous:n,language:r});case 4:case"end":return t.stop()}},t)}));return function(){return t.apply(this,arguments)}}(),stopListening:function(){var t=f(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=w.getRecognitionManager(),t.next=3,e.stopListening();case 3:case"end":return t.stop()}},t)}));return function(){return t.apply(this,arguments)}}(),abortListening:function(){var t=f(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=w.getRecognitionManager(),t.next=3,e.abortListening();case 3:case"end":return t.stop()}},t)}));return function(){return t.apply(this,arguments)}}(),browserSupportsSpeechRecognition:function(){return m},browserSupportsContinuousListening:function(){return b}};e.default=w},8979:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},9070:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.isNative=void 0;var n="undefined"!=typeof window&&(window.SpeechRecognition||window.webkitSpeechRecognition||window.mozSpeechRecognition||window.msSpeechRecognition||window.oSpeechRecognition);e.isNative=function(t){return t===n},e.default=n},9588:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]])},9946:(t,e,n)=>{"use strict";n.d(e,{A:()=>h});var r=n(2115);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,n)=>n?n.toUpperCase():e.toLowerCase()),a=t=>{let e=o(t);return e.charAt(0).toUpperCase()+e.slice(1)},s=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.filter((t,e,n)=>!!t&&""!==t.trim()&&n.indexOf(t)===e).join(" ").trim()},c=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((t,e)=>{let{color:n="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:a,className:l="",children:h,iconNode:p,...f}=t;return(0,r.createElement)("svg",{ref:e,...u,width:i,height:i,stroke:n,strokeWidth:a?24*Number(o)/Number(i):o,className:s("lucide",l),...!h&&!c(f)&&{"aria-hidden":"true"},...f},[...p.map(t=>{let[e,n]=t;return(0,r.createElement)(e,n)}),...Array.isArray(h)?h:[h]])}),h=(t,e)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:c,...u}=n;return(0,r.createElement)(l,{ref:o,iconNode:e,className:s("lucide-".concat(i(a(t))),"lucide-".concat(t),c),...u})});return n.displayName=a(t),n}}}]);