exports.id=762,exports.ids=[762],exports.modules={25:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},507:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(687),a=s(3210),l=s(2688);let n=(0,l.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),o=(0,l.A)("bell-dot",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M13.916 2.314A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.74 7.327A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673 9 9 0 0 1-.585-.665",key:"1tip0g"}],["circle",{cx:"18",cy:"8",r:"3",key:"1g0gzu"}]]),i=(0,l.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),d=(0,l.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),c=(0,l.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),m=(0,l.A)("circle-user",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]),h=({onToggleSidebar:e})=>{let[t,s]=(0,a.useState)(!1);return(0,r.jsxs)("header",{className:"border-b bg-white px-4 sm:px-6 py-4 sm:py-5 shrink-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("button",{onClick:e,className:"lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"Toggle sidebar",children:(0,r.jsx)(n,{className:"h-5 w-5 text-gray-600"})}),(0,r.jsx)("div",{className:"text-lg sm:text-xl font-semibold text-gray-900",children:"AI Interview"})]}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center gap-4 xl:gap-6",children:[(0,r.jsx)(o,{className:"h-6 w-6 sm:h-8 sm:w-7 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-700 cursor-pointer bg-gray-50 py-2 sm:py-4 px-4 sm:px-6 rounded-full transition-colors",children:[(0,r.jsx)(i,{className:"h-5 w-5 sm:h-6 sm:w-6"}),(0,r.jsx)("span",{className:"font-bold hidden sm:inline",children:"English"}),(0,r.jsx)(d,{className:"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dddae5] border border-[#aba6bb] text-[#aba6bb]"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer bg-gray-50 rounded-full px-4 sm:px-8 py-2 sm:py-3 transition-colors",children:[(0,r.jsx)("div",{className:"h-6 w-6 sm:h-8 sm:w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs sm:text-sm font-medium text-gray-500 p-1"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xs sm:text-sm font-bold text-gray-900",children:"Hammad M"}),(0,r.jsx)("span",{className:"text-xs text-purple-600",children:"Free"})]}),(0,r.jsx)(d,{className:"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]"})]})]}),(0,r.jsxs)("div",{className:"hidden md:flex lg:hidden items-center gap-3",children:[(0,r.jsx)(o,{className:"h-6 w-6 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 cursor-pointer bg-gray-50 rounded-full px-3 py-2 transition-colors",children:[(0,r.jsx)("div",{className:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs font-medium text-gray-500",children:"H"})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900",children:"Hammad"}),(0,r.jsx)(d,{className:"h-4 w-4 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]"})]})]}),(0,r.jsx)("div",{className:"sm:hidden",children:(0,r.jsx)("button",{onClick:()=>{s(!t)},className:"p-2",children:t?(0,r.jsx)(c,{className:"h-6 w-6"}):(0,r.jsx)(m,{className:"h-6 w-6 text-gray-700"})})})]}),t&&(0,r.jsxs)("div",{className:"mt-4 pb-4 flex flex-col gap-4 sm:hidden border-t pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-gray-50",children:[(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"H"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900",children:"Hammad M"}),(0,r.jsx)("span",{className:"text-xs text-purple-600",children:"Free"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(o,{className:"h-5 w-5 text-gray-700"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Notifications"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(i,{className:"h-5 w-5 text-gray-700"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"English"})]})]})]})};var x=s(474),p=s(5814),u=s.n(p),b=s(6189);let g={src:"/_next/static/media/logo-light.a0bdc026.svg",height:62,width:177,blurWidth:0,blurHeight:0},f=[{label:"Dashboard",href:"/",icon:(0,l.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},{label:"Job Posts",href:"/interview",icon:s(659).A}],v=({isOpen:e,onClose:t})=>{let s=(0,b.usePathname)(),l=(0,a.useRef)(s);return(0,a.useEffect)(()=>{l.current!==s&&e&&t&&t(),l.current=s},[s,e,t]),(0,a.useEffect)(()=>{let s=e=>{let s=document.getElementById("mobile-sidebar"),r=document.getElementById("sidebar-overlay");s&&!s.contains(e.target)&&r?.contains(e.target)&&t&&t()};return e?(document.addEventListener("mousedown",s),document.body.style.overflow="hidden"):document.body.style.overflow="unset",()=>{document.removeEventListener("mousedown",s),document.body.style.overflow="unset"}},[e,t]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("aside",{className:"hidden lg:flex w-54 h-full bg-white border-r p-6 flex-col shrink-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-10",children:(0,r.jsx)(x.default,{src:g,alt:"Logo"})}),(0,r.jsx)("nav",{className:"flex flex-col gap-4",children:f.map(e=>{let t=s===e.href,a=e.icon;return(0,r.jsxs)(u(),{href:e.href,className:`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group
                  ${t?"bg-purple-100 text-purple-700 font-extrabold":"text-gray-400 hover:bg-gray-50 hover:text-gray-600"}`,children:[(0,r.jsx)(a,{className:`w-5 h-5 ${t?"text-purple-700":"text-gray-400"}`}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.label)})})]}),(0,r.jsx)("div",{id:"sidebar-overlay",className:`fixed inset-0 bg-gray-300/50 bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 ${e?"opacity-100":"opacity-0 pointer-events-none"}`,children:(0,r.jsxs)("aside",{id:"mobile-sidebar",className:`fixed left-0 top-0 h-full w-64 bg-white border-r p-6 flex flex-col z-50 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-10",children:[(0,r.jsx)(x.default,{src:g,alt:"Logo"}),(0,r.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(c,{className:"h-5 w-5 text-gray-500"})})]}),(0,r.jsx)("nav",{className:"flex flex-col gap-4",children:f.map(e=>{let a=s===e.href,l=e.icon;return(0,r.jsxs)(u(),{href:e.href,className:`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group
                      ${a?"bg-purple-100 text-purple-700 font-extrabold":"text-gray-400 hover:bg-gray-50 hover:text-gray-600"}`,onClick:t,children:[(0,r.jsx)(l,{className:`w-5 h-5 ${a?"text-purple-700":"text-gray-400"}`}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.label)})})]})})]})},y=({children:e})=>{let[t,s]=(0,a.useState)(!1),l=(0,a.useCallback)(()=>{s(e=>!e)},[]),n=(0,a.useCallback)(()=>{s(!1)},[]);return(0,r.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,r.jsx)(v,{isOpen:t,onClose:n}),(0,r.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden min-w-0",children:[(0,r.jsx)(h,{onToggleSidebar:l}),(0,r.jsx)("main",{className:"flex-1 overflow-auto p-4 sm:p-6",children:e})]})]})}},659:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("briefcase-business",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},1279:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(687);s(3210);var a=s(218);let l=({children:e,...t})=>(0,r.jsx)(a.N,{...t,children:e})},2528:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(root)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx","default")},2569:(e,t,s)=>{Promise.resolve().then(s.bind(s,2528))},2688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:l="",children:n,iconNode:c,...m},h)=>(0,r.createElement)("svg",{ref:h,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:o("lucide",l),...!n&&!i(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(n)?n:[n]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...l},i)=>(0,r.createElement)(c,{ref:i,iconNode:t,className:o(`lucide-${a(n(e))}`,`lucide-${e}`,s),...l}));return s.displayName=n(e),s}},2704:()=>{},2910:(e,t,s)=>{Promise.resolve().then(s.bind(s,363)),Promise.resolve().then(s.bind(s,25)),Promise.resolve().then(s.bind(s,2175))},4013:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},4593:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>n});var r=s(687),a=s(218),l=s(2581);let n=({...e})=>{let{theme:t="system"}=(0,a.D)();return(0,r.jsx)(l.l$,{theme:t,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},5958:(e,t,s)=>{Promise.resolve().then(s.bind(s,4593)),Promise.resolve().then(s.bind(s,1279)),Promise.resolve().then(s.bind(s,9208))},6749:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},6814:(e,t,s)=>{"use strict";s.d(t,{Y9:()=>n,j2:()=>d});var r=s(9859),a=s(3560),l=s(6056);let{handlers:n,signIn:o,signOut:i,auth:d}=(0,r.Ay)({providers:[a.A,l.A],secret:process.env.AUTH_SECRET})},8014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>h});var r=s(7413),a=s(363),l=s(6649),n=s.n(l),o=s(5843),i=s.n(o);s(2704);var d=s(25),c=s(2175),m=s(6814);let h={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}},x=async({children:e})=>{let t=await (0,m.j2)();return(0,r.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsx)(c.SessionProvider,{session:t,children:(0,r.jsxs)("body",{className:`${n().className} ${i().variable} antialiased`,children:[(0,r.jsx)(d.default,{attribute:"class",defaultTheme:"light",children:e}),(0,r.jsx)(a.Toaster,{})]})})})}},9017:(e,t,s)=>{Promise.resolve().then(s.bind(s,507))}};